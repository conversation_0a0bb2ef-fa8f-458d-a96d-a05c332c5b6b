<!doctype html>
<html lang="en" class="light">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light only" />
    <title>Illustration Portal</title>
    <style>
      /* Force light theme and prevent dark mode */
      :root {
        color-scheme: light only;
      }
      html {
        color-scheme: light only;
      }
      @media (prefers-color-scheme: dark) {
        :root {
          color-scheme: light only;
        }
        html {
          color-scheme: light only;
        }
      }
    </style>
  </head>
  <body class="light">
    <div id="root"></div>
    <script>
      // Force light theme immediately on page load
      document.documentElement.classList.add('light');
      document.documentElement.classList.remove('dark');
      document.body.classList.add('light');
      document.body.classList.remove('dark');

      // Override any system theme detection
      if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', function() {
          // Always force light theme regardless of system preference
          document.documentElement.classList.add('light');
          document.documentElement.classList.remove('dark');
          document.body.classList.add('light');
          document.body.classList.remove('dark');
        });
      }
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
