import React from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import PolicySelection from './PolicySelection';
import IllustrationMainPage from './IllustrationMainPage';
import AnalysisReports from './AnalysisReports';
import SelectedScenarios from './SelectedScenarios';
import Settings from './Settings';

const Dashboard: React.FC = () => {
  const { activeTab } = useDashboard();

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'policy-selection':
        return <PolicySelection />;
      case 'illustrations':
      case 'as-is':
      case 'face-amount':
      case 'premium':
      case 'income':
      case 'loan-repayment':
      case 'interest-rate':
        return <IllustrationMainPage />;
      case 'analysis-reports':
        return <AnalysisReports />;
      case 'selected-scenarios':
        return <SelectedScenarios />;
      case 'settings':
        return <Settings />;
      default:
        return <PolicySelection />;
    }
  };

  return (
    <div className="flex-1 overflow-auto bg-gray-50 transition-colors duration-300">
      <div className="min-h-full">
        {renderActiveTab()}
      </div>
    </div>
  );
};

export default Dashboard;