<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Date Formatting Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>Date Formatting Test - MM-DD-YYYY Format</h1>
    <div id="results"></div>

    <script>
        // Copy of our date formatting utility
        const formatDateToMMDDYYYY = (dateString) => {
            if (!dateString) return 'N/A';

            try {
                let date;

                // If it's already in MM-DD-YYYY format, parse it correctly
                if (dateString.includes('-') && dateString.split('-')[0].length <= 2) {
                    const [month, day, year] = dateString.split('-');
                    date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
                }
                // If it's in YYYY-MM-DD format
                else if (dateString.includes('-') && dateString.split('-')[0].length === 4) {
                    date = new Date(dateString);
                }
                // If it's in other formats, try to parse directly
                else {
                    date = new Date(dateString);
                }

                // Check if date is valid
                if (isNaN(date.getTime())) {
                    return dateString; // Return original if can't parse
                }

                // Format to MM-DD-YYYY
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                const year = date.getFullYear();

                return `${month}-${day}-${year}`;
            } catch (error) {
                console.warn('Error formatting date:', dateString, error);
                return dateString || 'N/A';
            }
        };

        // Test cases
        const testCases = [
            { input: '2024-01-15', expected: '01-15-2024', description: 'YYYY-MM-DD format' },
            { input: '01-15-2024', expected: '01-15-2024', description: 'Already MM-DD-YYYY format' },
            { input: '2024/01/15', expected: '01-15-2024', description: 'YYYY/MM/DD format' },
            { input: '01/15/2024', expected: '01-15-2024', description: 'MM/DD/YYYY format' },
            { input: '15.01.2024', expected: '01-15-2024', description: 'DD.MM.YYYY format' },
            { input: '2024-12-25', expected: '12-25-2024', description: 'Christmas 2024' },
            { input: '2024-02-29', expected: '02-29-2024', description: 'Leap year date' },
            { input: null, expected: 'N/A', description: 'Null input' },
            { input: undefined, expected: 'N/A', description: 'Undefined input' },
            { input: '', expected: 'N/A', description: 'Empty string' },
            { input: 'invalid-date', expected: 'invalid-date', description: 'Invalid date string' }
        ];

        const resultsDiv = document.getElementById('results');
        let allPassed = true;

        testCases.forEach((testCase, index) => {
            const result = formatDateToMMDDYYYY(testCase.input);
            const passed = result === testCase.expected;
            allPassed = allPassed && passed;

            const div = document.createElement('div');
            div.className = `test-case ${passed ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>Test ${index + 1}:</strong> ${testCase.description}<br>
                <strong>Input:</strong> ${testCase.input}<br>
                <strong>Expected:</strong> ${testCase.expected}<br>
                <strong>Result:</strong> ${result}<br>
                <strong>Status:</strong> ${passed ? '✅ PASS' : '❌ FAIL'}
            `;
            resultsDiv.appendChild(div);
        });

        // Summary
        const summaryDiv = document.createElement('div');
        summaryDiv.className = `test-case ${allPassed ? 'success' : 'error'}`;
        summaryDiv.innerHTML = `
            <h2>Summary</h2>
            <strong>All tests ${allPassed ? 'PASSED' : 'FAILED'}</strong><br>
            Date formatting utility is ${allPassed ? 'working correctly' : 'needs fixes'}.
        `;
        resultsDiv.appendChild(summaryDiv);
    </script>
</body>
</html> 