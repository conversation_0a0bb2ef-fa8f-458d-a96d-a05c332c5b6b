<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Mapping Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .table-preview {
            max-height: 300px;
            overflow: auto;
            border: 1px solid #ddd;
            margin-top: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            white-space: nowrap;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        .scenario-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Table Mapping Test for Selected Scenarios</h1>
        <p>This test verifies that the correct table configurations are mapped to different scenario types.</p>
        
        <div class="test-section">
            <div class="test-title">🧪 Test Scenario Mappings</div>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // Mock scenario data for testing
        const testScenarios = [
            {
                id: 1,
                name: "As-Is Current Policy",
                category: "as-is",
                description: "Current policy performance"
            },
            {
                id: 2,
                name: "Face Amount Change",
                category: "face-amount",
                description: "Change face amount to $500,000"
            },
            {
                id: 3,
                name: "Modify Face Amount by Year",
                category: "face-amount",
                description: "Modify face amount varies by year"
            },
            {
                id: 4,
                name: "Premium Amount Change",
                category: "premium",
                description: "Change premium to new amount"
            },
            {
                id: 5,
                name: "Stop Paying Future Premium",
                category: "premium",
                description: "Stop paying future premium now"
            }
        ];

        // Mock table mapping function (simplified version of the actual function)
        function getDetailedTableConfig(scenario) {
            const scenarioName = scenario.name?.toLowerCase() || '';
            const scenarioCategory = scenario.category || '';
            
            // As-Is scenarios
            if (scenarioCategory === 'as-is' || scenarioName.includes('as-is')) {
                return {
                    tableName: 'As_Is_Loan',
                    title: 'As-Is Cash Value Analysis',
                    expectedColumns: ['Policy ID', 'Scenario ID', 'Policy Year', 'Calendar Year', 'Age', 'Premium', 'Cash Value', 'Death Benefit', 'Loan Outstanding', 'Charges', 'Net Cash Value']
                };
            }
            
            // Face Amount scenarios
            if (scenarioCategory === 'face-amount' || scenarioName.includes('face amount')) {
                // Check if it's modify by year scenario
                if (scenarioName.includes('modify') || scenarioName.includes('by year') || scenarioName.includes('varies')) {
                    return {
                        tableName: 'Face_Amount_Varies_By_Year',
                        title: 'Face Amount Varies By Year Analysis',
                        expectedColumns: ['Policy ID', 'Scenario ID', 'Policy Year', 'Calendar Year', 'Age', 'Premium', 'Cash Value', 'Death Benefit', 'Loan Outstanding', 'Charges', 'Net Cash Value']
                    };
                } else {
                    return {
                        tableName: 'Face_Amount',
                        title: 'Face Amount Analysis',
                        expectedColumns: ['Policy ID', 'Scenario ID', 'Policy Year', 'Calendar Year', 'Age', 'Premium', 'Cash Value', 'Death Benefit', 'Loan Outstanding', 'Charges', 'Net Cash Value']
                    };
                }
            }
            
            // Premium scenarios
            if (scenarioCategory === 'premium' || scenarioName.includes('premium')) {
                // Check if it's stop premium scenario
                if (scenarioName.includes('stop') || scenarioName.includes('cease') || scenarioName.includes('future premium')) {
                    return {
                        tableName: 'Stop_Premium_Amount',
                        title: 'Stop Premium Analysis',
                        expectedColumns: ['Policy ID', 'Scenario ID', 'Policy Year', 'Calendar Year', 'Age', 'Premium', 'Cash Value', 'Death Benefit', 'Loan Outstanding', 'Charges', 'Net Cash Value']
                    };
                } else {
                    return {
                        tableName: 'Premium_Amount',
                        title: 'Premium Amount Analysis',
                        expectedColumns: ['Policy ID', 'Scenario ID', 'Policy Year', 'Calendar Year', 'Age', 'Premium', 'Cash Value', 'Death Benefit', 'Loan Outstanding', 'Charges', 'Net Cash Value']
                    };
                }
            }
            
            // Default to As-Is table
            return {
                tableName: 'As_Is_Loan',
                title: 'Default Analysis',
                expectedColumns: ['Policy ID', 'Scenario ID', 'Policy Year', 'Calendar Year', 'Age', 'Premium', 'Cash Value', 'Death Benefit', 'Loan Outstanding', 'Charges', 'Net Cash Value']
            };
        }

        // Run tests
        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            let allTestsPassed = true;

            testScenarios.forEach((scenario, index) => {
                const config = getDetailedTableConfig(scenario);
                
                const testDiv = document.createElement('div');
                testDiv.className = 'scenario-info';
                
                const isCorrectMapping = validateMapping(scenario, config);
                
                testDiv.innerHTML = `
                    <strong>Scenario ${index + 1}:</strong> ${scenario.name}<br>
                    <strong>Category:</strong> ${scenario.category}<br>
                    <strong>Expected Table:</strong> ${getExpectedTable(scenario)}<br>
                    <strong>Actual Table:</strong> ${config.tableName}<br>
                    <strong>Title:</strong> ${config.title}<br>
                    <div class="test-result ${isCorrectMapping ? 'success' : 'error'}">
                        ${isCorrectMapping ? '✅ PASS - Correct table mapping' : '❌ FAIL - Incorrect table mapping'}
                    </div>
                `;
                
                resultsContainer.appendChild(testDiv);
                
                if (!isCorrectMapping) {
                    allTestsPassed = false;
                }
            });

            // Add summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-result ${allTestsPassed ? 'success' : 'error'}`;
            summaryDiv.innerHTML = `
                <h3>${allTestsPassed ? '🎉 All Tests Passed!' : '❌ Some Tests Failed'}</h3>
                <p>Table mapping functionality is ${allTestsPassed ? 'working correctly' : 'not working as expected'}.</p>
            `;
            resultsContainer.appendChild(summaryDiv);
        }

        function getExpectedTable(scenario) {
            const scenarioName = scenario.name?.toLowerCase() || '';
            const scenarioCategory = scenario.category || '';
            
            if (scenarioCategory === 'as-is') return 'As_Is_Loan';
            if (scenarioCategory === 'face-amount') {
                if (scenarioName.includes('modify') || scenarioName.includes('by year') || scenarioName.includes('varies')) {
                    return 'Face_Amount_Varies_By_Year';
                }
                return 'Face_Amount';
            }
            if (scenarioCategory === 'premium') {
                if (scenarioName.includes('stop') || scenarioName.includes('future premium')) {
                    return 'Stop_Premium_Amount';
                }
                return 'Premium_Amount';
            }
            return 'As_Is_Loan';
        }

        function validateMapping(scenario, config) {
            const expected = getExpectedTable(scenario);
            return config.tableName === expected;
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
