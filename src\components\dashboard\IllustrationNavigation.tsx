import React, { useMemo } from 'react';
import {
  Bookmark,
  DollarSign,
  TrendingUp,
  Download,
  Percent,
  Bug
} from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

interface IllustrationTab {
  id: string;
  typeId: number; // Unique ID from 1 to 6 for backend filtering
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}

const IllustrationNavigation: React.FC = () => {
  const { activeTab, setActiveTab, allowedIllustrationTypes } = useDashboard();

  const illustrationTabs: IllustrationTab[] = [
    {
      id: 'selected-scenarios',
      typeId: 0, // Special case - always shown
      label: 'Selected Scenarios',
      icon: Bookmark,
      description: 'View all selected scenarios grouped by illustration'
    },
    {
      id: 'as-is',
      typeId: 1,
      label: 'AS-IS',
      icon: Bookmark,
      description: 'Current policy baseline illustration'
    },
    {
      id: 'face-amount',
      typeId: 2,
      label: 'Face Amount',
      icon: DollarSign,
      description: 'Death benefit amount scenarios'
    },
    {
      id: 'premium',
      typeId: 3,
      label: 'Premium',
      icon: TrendingUp,
      description: 'Premium payment scenarios'
    },
    {
      id: 'interest-rate',
      typeId: 4,
      label: 'Interest Rate',
      icon: Percent,
      description: 'Interest rate based scenarios'
    },
    {
      id: 'income',
      typeId: 5,
      label: 'Full Surrender / Income (Loan & Withdrawal)',
      icon: Download,
      description: 'Loan & withdrawal scenarios'
    },
    {
      id: 'loan-repayment',
      typeId: 6,
      label: 'Loan Repayment',
      icon: Percent,
      description: 'Loan repayment scenarios'
    },
    {
      id: 'debug-types',
      typeId: -1, // Special debug tab - always shown
      label: 'Debug Types',
      icon: Bug,
      description: 'Debug illustration types status'
    }
  ];

  // 🔥 BACKEND FILTERING: Filter illustration tabs based on backend response
  const filteredIllustrationTabs = useMemo(() => {
    // Always filter based on backend response
    // Always show Selected Scenarios (typeId 0), Debug Types (typeId -1), filter others based on allowedIllustrationTypes
    return illustrationTabs.filter(tab =>
      tab.typeId === 0 || tab.typeId === -1 || allowedIllustrationTypes.includes(tab.typeId)
    );
  }, [allowedIllustrationTypes]);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="px-6 py-4">
        
        {/* Horizontal Navigation Tabs */}
        <div className="flex flex-wrap gap-2 overflow-x-auto">
          {filteredIllustrationTabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={`
                  flex items-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium
                  transition-all duration-200 whitespace-nowrap min-w-fit group relative
                  ${isActive
                    ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-lg transform scale-105'
                    : 'bg-gray-100 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700'
                  }
                `}
                title={tab.description}
              >
                <Icon className={`w-4 h-4 ${isActive ? 'text-white' : 'group-hover:text-blue-600 dark:group-hover:text-blue-400'} transition-colors`} />
                <span className="font-semibold">{tab.label}</span>
                {isActive && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full shadow-md"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default IllustrationNavigation;
