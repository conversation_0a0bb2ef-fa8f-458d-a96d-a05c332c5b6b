<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Localhost Navigation Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffd700;
        }
        .link-button {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 10px;
            margin: 10px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .link-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .refresh-button {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }
        .new-tab-button {
            background: linear-gradient(45deg, #a8edea, #fed6e3);
            color: #333;
        }
        .instructions {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #ffd700;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .expected {
            color: #90ee90;
            font-weight: bold;
        }
        .warning {
            color: #ffcccb;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Localhost Navigation Test</h1>
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            This page helps test the localhost link navigation behavior in the Life Insurance application.
        </p>
        
        <div class="test-section">
            <div class="test-title">🎯 Test Instructions</div>
            <div class="instructions">
                <div class="step">
                    <strong>Step 1:</strong> First, open the application and log in with credentials: 
                    <code>admin / password123</code>
                </div>
                <div class="step">
                    <strong>Step 2:</strong> Navigate around the app, search for policies, create some data
                </div>
                <div class="step">
                    <strong>Step 3:</strong> Use the buttons below to test different navigation scenarios
                </div>
                <div class="step">
                    <strong>Step 4:</strong> Observe the behavior and check console logs
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔗 Navigation Test Links</div>
            
            <div style="margin: 20px 0;">
                <h3>Fresh Navigation (Should go to login page):</h3>
                <a href="http://localhost:5173/" class="link-button" target="_blank">
                    🆕 Open in New Tab
                </a>
                <a href="http://localhost:5173/" class="link-button">
                    🔗 Navigate in Same Tab
                </a>
            </div>

            <div style="margin: 20px 0;">
                <h3>Page Refresh (Should maintain session):</h3>
                <button onclick="window.location.reload()" class="link-button refresh-button">
                    🔄 Refresh Current Page
                </button>
                <button onclick="refreshApp()" class="link-button refresh-button">
                    🔄 Refresh App Tab
                </button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">✅ Expected Behavior</div>
            <div class="step">
                <span class="expected">✅ Fresh Navigation (localhost links):</span> Should clear session and show login page
            </div>
            <div class="step">
                <span class="expected">✅ Page Refresh (F5, browser refresh):</span> Should maintain session and show dashboard with data
            </div>
            <div class="step">
                <span class="expected">✅ Sign Out:</span> Should clear data and show login page
            </div>
            <div class="step">
                <span class="warning">🔧 Recent Fix:</span> Page refresh now properly maintains session (was broken)
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 Debug Information</div>
            <div id="debug-info">
                <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                <p><strong>Referrer:</strong> <span id="referrer"></span></p>
                <p><strong>Session Storage:</strong> <span id="session-storage"></span></p>
                <p><strong>Local Storage Keys:</strong> <span id="local-storage"></span></p>
            </div>
        </div>
    </div>

    <script>
        // Update debug information
        function updateDebugInfo() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('referrer').textContent = document.referrer || 'none';
            
            // Check session storage
            const sessionKeys = [];
            for (let i = 0; i < sessionStorage.length; i++) {
                sessionKeys.push(sessionStorage.key(i));
            }
            document.getElementById('session-storage').textContent = sessionKeys.join(', ') || 'empty';
            
            // Check local storage
            const localKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('insuranceApp_')) {
                    localKeys.push(key);
                }
            }
            document.getElementById('local-storage').textContent = localKeys.join(', ') || 'empty';
        }

        // Refresh app tab if it exists
        function refreshApp() {
            // Try to find and refresh the app tab
            alert('Please manually refresh the application tab to test page refresh behavior');
        }

        // Update debug info on load
        updateDebugInfo();
        
        // Update debug info every 2 seconds
        setInterval(updateDebugInfo, 2000);

        console.log('🔗 Navigation test page loaded');
        console.log('🔍 Current URL:', window.location.href);
        console.log('🔍 Referrer:', document.referrer || 'none');
    </script>
</body>
</html>
