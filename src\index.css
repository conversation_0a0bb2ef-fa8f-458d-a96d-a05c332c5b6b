@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force light theme and prevent dark mode */
:root {
  color-scheme: light only;
}

html {
  color-scheme: light only;
}

/* Override any dark mode preferences */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: light only;
  }

  html {
    color-scheme: light only;
  }

  body {
    background-color: white;
    color: black;
  }
}

/* Ensure all elements use light theme */
* {
  color-scheme: light only;
}

/* Currency formatting styles for Excel-like table display */
.currency-cell {
  font-family: 'Courier New', monospace;
  text-align: right;
}

.currency-symbol {
  float: left;
}

.currency-amount {
  text-align: right;
}

/* Ensure proper spacing in currency cells */
.currency-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Table cell alignment utilities */
.table-cell-currency {
  text-align: right;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
  -webkit-font-feature-settings: "tnum";
  -moz-font-feature-settings: "tnum";
}

.table-cell-number {
  text-align: right;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
  -webkit-font-feature-settings: "tnum";
  -moz-font-feature-settings: "tnum";
}

.table-cell-text {
  text-align: left;
}

/* Monospace font for better number alignment */
.monospace-numbers {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
  -webkit-font-feature-settings: "tnum";
  -moz-font-feature-settings: "tnum";
}

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-track-gray-800 {
    scrollbar-color: #374151 #1f2937;
  }
  
  .scrollbar-thumb-gray-600 {
    scrollbar-color: #4b5563 #1f2937;
  }
  
  /* Webkit scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-track-gray-800::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 3px;
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 3px;
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
}



/* Custom focus styles for better accessibility */
*:focus {
  outline-color: #3b82f6;
}