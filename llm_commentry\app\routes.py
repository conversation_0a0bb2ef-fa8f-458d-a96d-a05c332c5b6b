from flask import Blueprint, request, jsonify
import json
from app.llm_service import get_llm_commentary
from app.prompt_builder import build_prompt

commentary_bp = Blueprint('commentary', __name__)

@commentary_bp.route('/commentary', methods=['GET'])
def commentary():
    try:
        # Get parameters from query string
        policy_number = request.args.get('policyNumber', '')
        customer_name = request.args.get('customerName', '')
        policy_type = request.args.get('policyType', '')
        scenario_name = request.args.get('scenarioName', '')
        scenario_category = request.args.get('scenarioCategory', '')
        scenario_data_str = request.args.get('scenarioData', '[]')

        # Parse scenario data from JSON string
        try:
            scenario_data = json.loads(scenario_data_str)
        except json.JSONDecodeError:
            return jsonify({
                "error": "Invalid scenario data format",
                "message": "Scenario data must be valid JSON"
            }), 400

        # Validate that we have scenario data
        if not scenario_data or len(scenario_data) == 0:
            return jsonify({
                "error": "No scenario data provided",
                "message": "Scenario data is required for AI commentary generation"
            }), 400

        # Build enhanced prompt with all available context
        prompt = build_prompt(scenario_data, {
            'policy_number': policy_number,
            'customer_name': customer_name,
            'policy_type': policy_type,
            'scenario_name': scenario_name,
            'scenario_category': scenario_category
        })

        # Get LLM commentary
        result = get_llm_commentary(prompt)

        # Validate that we got a proper response
        if not result or not result.get('formatted'):
            return jsonify({
                "error": "AI service unavailable",
                "message": "Unable to generate commentary at this time"
            }), 503

        return jsonify({"response": result})

    except Exception as e:
        print(f"❌ Commentary route error: {str(e)}")
        return jsonify({
            "error": "Internal server error",
            "message": "Failed to generate AI commentary"
        }), 500
