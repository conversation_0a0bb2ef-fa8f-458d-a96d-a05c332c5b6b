import openai
import os

# Azure OpenAI setup using the legacy SDK format
openai.api_type = "azure"
openai.api_base = os.getenv("AZURE_OPENAI_ENDPOINT")  # e.g., https://<your-resource>.openai.azure.com/
openai.api_version = "2024-12-01-preview"
openai.api_key = os.getenv("AZURE_OPENAI_API_KEY")

# Use your Azure Chat deployment name
DEPLOYMENT_NAME = os.getenv("AZURE_CHAT_DEPLOYMENT", "gpt-4o")

def get_llm_commentary(prompt):
    try:
        # Check if Azure OpenAI credentials are available
        if not openai.api_key or not openai.api_base:
            print("⚠️ Azure OpenAI credentials not found")
            return None

        response = openai.ChatCompletion.create(
            engine=DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": "You are a financial advisor AI assistant providing helpful and actionable insights for life insurance policy analysis. Provide clear, structured analysis with specific recommendations, risk considerations, and key insights."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.5,
            max_tokens=1500
        )

        raw_text = response['choices'][0]['message']['content']

        # Validate that we got actual content
        if not raw_text or raw_text.strip() == "":
            print("⚠️ Empty response from LLM service")
            return None

        # Format and clean the text
        cleaned_text = raw_text.encode('utf-8').decode('unicode_escape')
        cleaned_text = cleaned_text.replace("\\n", "\n").replace("\\u2019", "'").replace("â", "'").strip()
        cleaned_text = f"👋 Hello! Here's your policy analysis:\n\n{cleaned_text}"

        return {
            "raw": raw_text,
            "formatted": cleaned_text
        }
    except Exception as e:
        print(f"❌ Error in LLM service: {str(e)}")
        return None

def get_fallback_response():
    """Return an error response when LLM is not available"""
    return None  # Return None to indicate service unavailable