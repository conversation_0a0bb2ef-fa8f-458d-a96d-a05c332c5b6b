import React from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import Card from '../common/Card';
import { formatDateToMMDDYYYY } from '../../utils/dateFormatter';

const CATEGORY_LABELS: Record<string, string> = {
  'as-is': 'AS-IS',
  'face-amount': 'Face Amount',
  'premium': 'Premium',
  'income': 'Income',
  'loan-repayment': 'Loan Repayment',
  'interest-rate': 'Interest Rate',
};

const SelectedScenariosPage: React.FC = () => {
  const { scenarios, selectedScenarios } = useDashboard();

  // Group selected scenarios by category
  const grouped: Record<string, typeof scenarios> = {};
  scenarios
    .filter(s => selectedScenarios.includes(s.id))
    .forEach(s => {
      if (!grouped[s.category]) grouped[s.category] = [];
      grouped[s.category].push(s);
    });

  const hasSelected = Object.keys(grouped).length > 0;

  return (
    <div className="space-y-8">
      {!hasSelected && (
        <Card>
          <div className="text-center py-8">
            <span className="text-gray-500 dark:text-gray-400">No scenarios selected. Select scenarios from illustration tabs to see them here.</span>
          </div>
        </Card>
      )}
      {Object.entries(grouped).map(([category, scenarios]) => (
        <div key={category}>
          <h2 className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-2">{CATEGORY_LABELS[category] || category}</h2>
          <div className="space-y-4">
            {scenarios.map(scenario => (
              <Card key={scenario.id}>
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{scenario.name}</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Created: {formatDateToMMDDYYYY(scenario.createdAt)}</p>
                  </div>
                  <div className="mt-2 md:mt-0">
                    <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">{CATEGORY_LABELS[category] || category}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SelectedScenariosPage; 