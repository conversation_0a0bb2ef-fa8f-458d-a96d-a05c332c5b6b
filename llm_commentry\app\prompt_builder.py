import json

def build_prompt(scenario_data, policy_context=None):
    """
    Build a comprehensive prompt for AI commentary generation

    Args:
        scenario_data: List of scenario data points
        policy_context: Dictionary with policy information (optional)
    """

    # Build context section if policy information is available
    context_section = ""
    if policy_context:
        context_section = f"""
**POLICY CONTEXT:**
- Policy Number: {policy_context.get('policy_number', 'N/A')}
- Customer Name: {policy_context.get('customer_name', 'N/A')}
- Policy Type: {policy_context.get('policy_type', 'N/A')}
- Scenario Name: {policy_context.get('scenario_name', 'N/A')}
- Scenario Category: {policy_context.get('scenario_category', 'N/A')}
"""

    return f"""
You are a financial advisor AI assistant specializing in life insurance policy analysis.

{context_section}

Please analyze the following policy scenario data and provide a comprehensive analysis structured as follows:

**ANALYSIS SECTIONS REQUIRED:**

1. **OVERALL ASSESSMENT**: Provide a summary of the policy's performance and value proposition

2. **KEY STRENGTHS**: Highlight the positive aspects of this policy scenario

3. **GROWTH TRAJECTORY**: Analyze the cash value growth and death benefit progression

4. **RECOMMENDATIONS**: Provide specific, actionable recommendations for:
   - Premium payment strategy
   - Policy review schedule
   - Loan utilization (if applicable)
   - Optimization opportunities

5. **RISK CONSIDERATIONS**: Identify potential risks and considerations:
   - Interest rate sensitivity
   - Premium payment consistency requirements
   - Market condition impacts
   - Performance monitoring needs

6. **KEY INSIGHTS**: Provide important insights about:
   - Long-term value accumulation
   - Liquidity potential
   - Protection vs. investment balance

**POLICY SCENARIO DATA:**
{json.dumps(scenario_data, indent=2)}

Please provide clear, professional analysis that a client can easily understand. Use specific numbers from the data when relevant, and ensure recommendations are practical and actionable.

IMPORTANT: Structure your response with clear section headers and bullet points for easy parsing.
"""
