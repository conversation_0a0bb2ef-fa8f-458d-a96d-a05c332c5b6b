import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FileText, Trash2, Bar<PERSON>hart3, MessageSquare, CheckSquare, Square } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';
import type { Scenario as BaseScenario } from '../../types';

type ScenarioWithKeyPoints = BaseScenario & { keyPoints?: string[] };

const SelectedScenariosNew: React.FC = () => {
  const { scenarios, selectedCustomerData, selectedPolicyData, setActiveTab, deleteScenario, addScenario, loadScenariosFromBackend } = useDashboard();
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [calculatingScenarios, setCalculatingScenarios] = useState<Set<string>>(new Set());
  const [isLoadingScenarios, setIsLoadingScenarios] = useState(false);
  const [isComponentReady, setIsComponentReady] = useState(false);
  
  // Completely stable scenarios state to prevent ANY flickering
  const [stableScenarios, setStableScenarios] = useState<ScenarioWithKeyPoints[]>([]);
  const [lastScenariosLength, setLastScenariosLength] = useState<number>(0);
  const [hasInitialized, setHasInitialized] = useState<boolean>(false);

  // Ultra-stable scenarios update mechanism - only updates when length changes significantly
  useEffect(() => {
    // Only update if scenarios length changed significantly or first time
    if (!hasInitialized || Math.abs(scenarios.length - lastScenariosLength) > 0) {
      console.log('🔄 Scenarios count changed, updating stable scenarios:', scenarios.length);

      const filteredScenarios = (scenarios as ScenarioWithKeyPoints[]).filter(scenario => {
        return true; // Show all current session scenarios
      });

      // Sort scenarios by creation date (newest first) to maintain consistent order
      const sortedScenarios = filteredScenarios.sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return dateB - dateA;
      });

      setStableScenarios(sortedScenarios);
      setLastScenariosLength(scenarios.length);
      setHasInitialized(true);

      // Mark component as ready after first initialization
      if (!isComponentReady) {
        setTimeout(() => setIsComponentReady(true), 100);
      }
    }
  }, [scenarios.length, lastScenariosLength, hasInitialized, isComponentReady]);

  // Use stable scenarios for display - NEVER changes unless scenarios count changes
  const displayScenarios: ScenarioWithKeyPoints[] = useMemo(() => {
    return stableScenarios; // Always return stable scenarios
  }, [stableScenarios]);

  // Minimal logging to prevent console spam
  useEffect(() => {
    if (displayScenarios.length > 0) {
      console.log('🔍 SelectedScenarios stable count:', displayScenarios.length);
    }
  }, [displayScenarios.length]);

  const handleScenarioClick = useCallback(async (scenarioId: string) => {
    console.log('🎯 Scenario card clicked:', scenarioId);

    // Toggle selection
    const newSelectedScenario = selectedScenario === scenarioId ? null : scenarioId;
    setSelectedScenario(newSelectedScenario);

    // If selecting a scenario, trigger backend calculation
    if (newSelectedScenario) {
      const scenario = displayScenarios.find(s => s.id === scenarioId);
      if (scenario && selectedPolicyData) {
        // Only trigger calculation for backend scenarios with real database IDs
        if (scenario.data?.backendData?.scenario_id && scenario.data?.backendData?.policy_id) {
          console.log('🚀 Triggering backend calculation for database scenario:', {
            scenarioId: scenario.data.backendData.scenario_id,
            policyId: scenario.data.backendData.policy_id
          });

          try {
            // Set calculating state
            setCalculatingScenarios(prev => new Set(prev).add(scenarioId));

            // TODO: Add actual backend calculation call here
            // await triggerBackendCalculation(scenario.data.backendData.scenario_id, scenario.data.backendData.policy_id);

            // Remove calculating state after completion
            setTimeout(() => {
              setCalculatingScenarios(prev => {
                const newSet = new Set(prev);
                newSet.delete(scenarioId);
                return newSet;
              });
            }, 2000);
          } catch (error) {
            console.error('❌ Error triggering backend calculation:', error);
            // Remove calculating state on error
            setCalculatingScenarios(prev => {
              const newSet = new Set(prev);
              newSet.delete(scenarioId);
              return newSet;
            });
          }
        } else {
          console.log('ℹ️ Local scenario selected - no backend calculation needed:', {
            scenarioId: scenarioId,
            isLocal: !scenario.data?.backendData?.scenario_id
          });
        }
      }
    }
  }, [selectedScenario, displayScenarios, selectedPolicyData]);

  const handleDeleteScenario = useCallback(async (scenarioId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click

    if (window.confirm('Are you sure you want to delete this scenario? This action cannot be undone.')) {
      try {
        await deleteScenario(scenarioId);
        // If the deleted scenario was currently selected, clear the selection
        if (selectedScenario === scenarioId) {
          setSelectedScenario(null);
        }
      } catch (error) {
        console.error('Failed to delete scenario:', error);
        alert('Failed to delete scenario. Please try again.');
      }
    }
  }, [deleteScenario, selectedScenario]);

  // Load backend scenarios when policy is selected - WITH DEBOUNCE
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const fetchDataForPolicy = async () => {
      if (!selectedPolicyData?.id) {
        console.log('🚫 No policy selected, skipping data fetch');
        setIsLoadingScenarios(false);
        return;
      }

      console.log('🔍 Loading backend scenarios for policy:', selectedPolicyData.id);
      setIsLoadingScenarios(true);

      try {
        // Load scenarios from backend database
        await loadScenariosFromBackend(parseInt(selectedPolicyData.id));
        console.log('✅ Backend scenarios loaded successfully');
      } catch (error) {
        console.error('❌ Error loading backend scenarios:', error);
      } finally {
        // Add a delay to prevent flickering
        timeoutId = setTimeout(() => {
          setIsLoadingScenarios(false);
        }, 500);
      }
    };

    // Debounce the fetch call to prevent rapid successive calls
    const debouncedFetch = setTimeout(() => {
      fetchDataForPolicy();
    }, 100);

    return () => {
      clearTimeout(debouncedFetch);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [selectedPolicyData?.id]); // Remove dependencies that cause frequent re-runs

  return (
    <div className="space-y-6 px-6">
      {/* Current Policy Information Card - Only Show After Policy Selection */}
      {selectedPolicyData && (
        <Card className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Current Policy Information</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Policy Number:</span>
              <div className="font-medium text-gray-900">{selectedPolicyData.policyNumber || 'N/A'}</div>
            </div>
            <div>
              <span className="text-gray-500">Customer:</span>
              <div className="font-medium text-gray-900">{selectedCustomerData?.name || 'N/A'}</div>
            </div>
            <div>
              <span className="text-gray-500">Policy Type:</span>
              <div className="font-medium text-gray-900">{selectedPolicyData.name || 'N/A'}</div>
            </div>
          </div>
        </Card>
      )}

      {/* Loading State for Scenarios */}
      {selectedPolicyData && isLoadingScenarios && (
        <div className="flex items-center justify-center h-32 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-gray-600">Loading scenarios...</p>
          </div>
        </div>
      )}

      {/* Scenarios Grid - New Stable Design */}
      {selectedPolicyData && !isLoadingScenarios && isComponentReady && displayScenarios.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">Saved Scenarios</h2>
            <div className="text-sm text-gray-500">
              {displayScenarios.length} scenario{displayScenarios.length !== 1 ? 's' : ''} found
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {displayScenarios.map((scenario) => {
              // Create a stable key that includes scenario ID and important data
              const stableKey = `scenario-${scenario.id}-${scenario.data?.backendData?.scenario_id || 'local'}`;
              const isSelected = selectedScenario === scenario.id;
              const isDatabase = !!scenario.data?.backendData?.scenario_id;
              
              return (
                <ScenarioCard
                  key={stableKey}
                  scenario={scenario}
                  isSelected={isSelected}
                  isDatabase={isDatabase}
                  calculatingScenarios={calculatingScenarios}
                  onScenarioClick={handleScenarioClick}
                  onDeleteScenario={handleDeleteScenario}
                />
              );
            })}
          </div>
        </div>
      )}

      {/* No Scenarios Message */}
      {selectedPolicyData && !isLoadingScenarios && displayScenarios.length === 0 && (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Scenarios Found</h3>
          <p className="text-gray-600 mb-4">
            No scenarios have been saved for this policy yet. Create illustrations to see scenarios here.
          </p>
          <Button
            onClick={() => setActiveTab('as-is')}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Create First Scenario
          </Button>
        </div>
      )}
    </div>
  );
};

// Separate ScenarioCard component to prevent re-renders - with deep comparison
const ScenarioCard: React.FC<{
  scenario: ScenarioWithKeyPoints;
  isSelected: boolean;
  isDatabase: boolean;
  calculatingScenarios: Set<string>;
  onScenarioClick: (id: string) => void;
  onDeleteScenario: (id: string, event: React.MouseEvent) => void;
}> = React.memo(({ scenario, isSelected, isDatabase, calculatingScenarios, onScenarioClick, onDeleteScenario }) => {
  return (
    <div
      onClick={() => onScenarioClick(scenario.id)}
      className={`
        relative bg-white rounded-xl border-2 p-6 cursor-pointer transition-all duration-300 transform hover:scale-[1.02]
        ${isSelected
          ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl ring-4 ring-blue-100'
          : 'border-gray-200 hover:border-blue-300 hover:shadow-lg'
        }
      `}
    >
      {/* Delete Button */}
      <button
        onClick={(e) => onDeleteScenario(scenario.id, e)}
        className="absolute top-4 right-4 p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-all duration-200 z-10"
        title="Delete scenario"
      >
        <Trash2 className="w-4 h-4" />
      </button>

      {/* Status Badge */}
      <div className="absolute top-4 left-4">
        {isDatabase ? (
          <div className="flex items-center space-x-1 bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Database</span>
          </div>
        ) : (
          <div className="flex items-center space-x-1 bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-xs font-medium">
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            <span>Local</span>
          </div>
        )}
      </div>

      {/* Selected Indicator */}
      {isSelected && (
        <div className="absolute top-4 right-16">
          <div className="flex items-center space-x-1 bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-xs font-medium">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>SELECTED</span>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="mt-12">
        {/* Title */}
        <h3 className="text-lg font-bold text-gray-900 mb-4">
          {scenario.name || (() => {
            switch(scenario.category) {
              case 'as-is': return 'AS-IS Illustrations';
              case 'face-amount': return 'Face Amount Illustrations';
              case 'premium': return 'Premium Illustrations';
              case 'income': return 'Income Illustrations';
              case 'loan-repayment': return 'Loan Repayment Illustrations';
              case 'interest-rate': return 'Interest Rate Illustrations';
              case 'policy-lapse': return 'Policy Lapse Illustrations';
              default: return 'Scenario Illustrations';
            }
          })()}
        </h3>
        
        {/* TOP SECTION: Scenario ID, Policy ID, Illustration ID */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          {isDatabase ? (
            // Backend scenario - show real database IDs
            <>
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 p-3 rounded-lg">
                <div className="text-xs text-blue-600 font-semibold uppercase tracking-wide">Scenario ID</div>
                <div className="text-lg font-bold text-blue-900">{scenario.data.backendData.scenario_id}</div>
              </div>
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 p-3 rounded-lg">
                <div className="text-xs text-gray-600 font-semibold uppercase tracking-wide">Policy ID</div>
                <div className="text-lg font-bold text-gray-900">{scenario.data.backendData.policy_id}</div>
              </div>
              <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 p-3 rounded-lg">
                <div className="text-xs text-green-600 font-semibold uppercase tracking-wide">Illustration ID</div>
                <div className="text-lg font-bold text-green-900">{scenario.data.backendData.illustration_id || 'N/A'}</div>
              </div>
            </>
          ) : (
            // Local scenario - show session ID
            <>
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 p-3 rounded-lg">
                <div className="text-xs text-orange-600 font-semibold uppercase tracking-wide">Session ID</div>
                <div className="text-lg font-bold text-orange-900">{scenario.id.slice(-8)}</div>
              </div>
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 p-3 rounded-lg">
                <div className="text-xs text-gray-600 font-semibold uppercase tracking-wide">Policy ID</div>
                <div className="text-lg font-bold text-gray-900">{scenario.policyId}</div>
              </div>
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 p-3 rounded-lg">
                <div className="text-xs text-gray-600 font-semibold uppercase tracking-wide">Illustration ID</div>
                <div className="text-lg font-bold text-gray-900">Local</div>
              </div>
            </>
          )}
        </div>

        {/* QUESTION SECTION */}
        <div className="mb-4 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <MessageSquare className="w-5 h-5 text-blue-600 mr-2" />
            <h4 className="text-sm text-blue-600 uppercase tracking-wide font-bold">Question</h4>
          </div>
          <p className="text-sm text-blue-900 font-medium leading-relaxed">
            {scenario.asIsDetails || 'No question specified'}
          </p>
        </div>

        {/* OPTION SECTION */}
        <div className="mb-4 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <CheckSquare className="w-5 h-5 text-green-600 mr-2" />
            <h4 className="text-sm text-green-600 uppercase tracking-wide font-bold">Option</h4>
          </div>
          <p className="text-sm text-green-900 font-medium leading-relaxed">
            {(() => {
              // Try multiple sources for option description
              const optionDesc = scenario.data?.backendData?.illustration_option_description ||
                               scenario.data?.formattedData?.optionDescription ||
                               (scenario.whatIfOptions && scenario.whatIfOptions.length > 0 ? scenario.whatIfOptions[0] : null);
              
              if (optionDesc) {
                return optionDesc;
              }
              
              // Generate option description based on scenario data
              const backendData = scenario.data?.backendData;
              if (backendData) {
                const parts = [];
                if (backendData.new_face_amount && backendData.new_face_amount > 0) {
                  parts.push(`Face Amount: $${backendData.new_face_amount.toLocaleString()}`);
                }
                if (backendData.new_premium_amount && backendData.new_premium_amount > 0) {
                  parts.push(`Premium: $${backendData.new_premium_amount.toLocaleString()}`);
                }
                if (backendData.current_interest_rate && backendData.current_interest_rate > 0) {
                  parts.push(`Interest Rate: ${(backendData.current_interest_rate * 100).toFixed(2)}%`);
                }
                if (backendData.retirement_age_goal) {
                  parts.push(`Retirement Age: ${backendData.retirement_age_goal}`);
                }
                if (parts.length > 0) {
                  return parts.join(', ');
                }
              }
              
              return 'No option specified';
            })()}
          </p>
        </div>

        {/* SELECTED OPTION SECTION */}
        <div className="mb-6 bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <Square className="w-5 h-5 text-purple-600 mr-2" />
            <h4 className="text-sm text-purple-600 uppercase tracking-wide font-bold">Selected Option</h4>
          </div>
          <p className="text-sm text-purple-900 font-medium leading-relaxed">
            {(() => {
              // For selected option, show the same as option but with "Selected: " prefix
              const optionDesc = scenario.data?.backendData?.illustration_option_description ||
                               scenario.data?.formattedData?.optionDescription ||
                               (scenario.whatIfOptions && scenario.whatIfOptions.length > 0 ? scenario.whatIfOptions[0] : null);
              
              if (optionDesc) {
                return `✓ ${optionDesc}`;
              }
              
              // Generate selected option description based on scenario data
              const backendData = scenario.data?.backendData;
              if (backendData) {
                const parts = [];
                if (backendData.new_face_amount && backendData.new_face_amount > 0) {
                  parts.push(`Face Amount: $${backendData.new_face_amount.toLocaleString()}`);
                }
                if (backendData.new_premium_amount && backendData.new_premium_amount > 0) {
                  parts.push(`Premium: $${backendData.new_premium_amount.toLocaleString()}`);
                }
                if (backendData.current_interest_rate && backendData.current_interest_rate > 0) {
                  parts.push(`Interest Rate: ${(backendData.current_interest_rate * 100).toFixed(2)}%`);
                }
                if (backendData.retirement_age_goal) {
                  parts.push(`Retirement Age: ${backendData.retirement_age_goal}`);
                }
                if (parts.length > 0) {
                  return `✓ ${parts.join(', ')}`;
                }
              }
              
              return 'No option selected';
            })()}
          </p>
        </div>

        {/* Action Button */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            {calculatingScenarios.has(scenario.id) && (
              <div className="flex items-center space-x-2 bg-yellow-100 text-yellow-700 px-3 py-1 rounded-full text-xs">
                <div className="w-3 h-3 border-2 border-yellow-600 border-t-transparent rounded-full animate-spin"></div>
                <span>Calculating...</span>
              </div>
            )}
          </div>
          <div className="text-blue-600 text-sm font-semibold">
            {calculatingScenarios.has(scenario.id) ? 'Processing...' : 'Click to View Details'}
          </div>
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  return (
    prevProps.scenario.id === nextProps.scenario.id &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.isDatabase === nextProps.isDatabase &&
    prevProps.calculatingScenarios.has(prevProps.scenario.id) === nextProps.calculatingScenarios.has(nextProps.scenario.id) &&
    prevProps.scenario.name === nextProps.scenario.name &&
    prevProps.scenario.data?.backendData?.scenario_id === nextProps.scenario.data?.backendData?.scenario_id
  );
});

export default SelectedScenariosNew;
