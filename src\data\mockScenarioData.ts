// Column definitions for the scenario table
export const ScenarioTableColumns = {
  POLICY_YEAR: 'Policy Year',
  END_OF_AGE: 'End of Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_OUTLAY: 'Net Outlay',
  NET_SURRENDER_VALUE: 'Net Surrender Value',
  NET_DEATH_BENEFIT: 'Net Death Benefit'
} as const;

// Column configurations with additional display properties
export const ScenarioTableColumnConfig = [
  { 
    key: 'policyYear',
    header: ScenarioTableColumns.POLICY_YEAR,
    width: 100
  },
  { 
    key: 'endOfAge',
    header: ScenarioTableColumns.END_OF_AGE,
    width: 100
  },
  { 
    key: 'plannedPremium',
    header: ScenarioTableColumns.PLANNED_PREMIUM,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netOutlay',
    header: ScenarioTableColumns.NET_OUTLAY,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netSurrenderValue',
    header: ScenarioTableColumns.NET_SURRENDER_VALUE,
    width: 180,
    isCurrency: true
  },
  { 
    key: 'netDeathBenefit',
    header: ScenarioTableColumns.NET_DEATH_BENEFIT,
    width: 180,
    isCurrency: true
  }
];

export interface ScenarioTableData {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

// ===== CASH VALUE ANALYSIS TABLE =====
export const As_Is_Loan = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const AsIsLoan = [
  { key: 'policyYear', header: As_Is_Loan.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: As_Is_Loan.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: As_Is_Loan.AGE, width: 80 },
  { key: 'plannedPremium', header: As_Is_Loan.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: As_Is_Loan.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: As_Is_Loan.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: As_Is_Loan.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: As_Is_Loan.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: As_Is_Loan.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: As_Is_Loan.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: As_Is_Loan.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: As_Is_Loan.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: As_Is_Loan.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: As_Is_Loan.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: As_Is_Loan.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: As_Is_Loan.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface AsIsLoanTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== DEATH BENEFIT ANALYSIS TABLE =====
export const Face_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const FaceAmount = [
  { key: 'policyYear', header: Face_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface FaceAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== POLICY PERFORMANCE TABLE =====
export const Face_Amount_Varies_By_Year = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const FaceAmountVariesByYear = [
  { key: 'policyYear', header: Face_Amount_Varies_By_Year.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount_Varies_By_Year.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount_Varies_By_Year.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount_Varies_By_Year.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount_Varies_By_Year.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount_Varies_By_Year.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount_Varies_By_Year.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount_Varies_By_Year.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount_Varies_By_Year.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount_Varies_By_Year.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount_Varies_By_Year.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount_Varies_By_Year.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount_Varies_By_Year.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount_Varies_By_Year.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount_Varies_By_Year.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount_Varies_By_Year.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface FaceAmountVariesByYearTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== RISK ASSESSMENT TABLE =====
export const Premium_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const PremiumAmount = [
  { key: 'policyYear', header: Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface PremiumAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const Stop_Premium_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const StopPremiumAmount = [
  { key: 'policyYear', header: Stop_Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Stop_Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Stop_Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Stop_Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Stop_Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Stop_Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Stop_Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Stop_Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Stop_Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Stop_Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Stop_Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Stop_Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Stop_Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Stop_Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Stop_Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Stop_Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface StopPremiumAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const generateMockTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Generating table data for scenario:', scenario.id, scenario.name);

  // Generate mock table data for 10 years (2025-2034)
  const tableData: ScenarioTableData[] = [
    {
      policyYear: 1,
      endOfAge: 40,
      plannedPremium: 10000,
      netOutlay: 5000,
      netSurrenderValue: 50000,
      netDeathBenefit: 250000
    },
    {
      policyYear: 2,
      endOfAge: 41,
      plannedPremium: 10000,
      netOutlay: 5500,
      netSurrenderValue: 51000,
      netDeathBenefit: 255000
    },
    {
      policyYear: 3,
      endOfAge: 42,
      plannedPremium: 10000,
      netOutlay: 6000,
      netSurrenderValue: 52000,
      netDeathBenefit: 260000
    },
    {
      policyYear: 4,
      endOfAge: 43,
      plannedPremium: 10000,
      netOutlay: 6500,
      netSurrenderValue: 53000,
      netDeathBenefit: 265000
    },
    {
      policyYear: 5,
      endOfAge: 44,
      plannedPremium: 10000,
      netOutlay: 7000,
      netSurrenderValue: 54000,
      netDeathBenefit: 270000
    },
    {
      policyYear: 6,
      endOfAge: 45,
      plannedPremium: 10000,
      netOutlay: 7500,
      netSurrenderValue: 55000,
      netDeathBenefit: 275000
    },
    {
      policyYear: 7,
      endOfAge: 46,
      plannedPremium: 10000,
      netOutlay: 8000,
      netSurrenderValue: 56000,
      netDeathBenefit: 280000
    },
    {
      policyYear: 8,
      endOfAge: 47,
      plannedPremium: 10000,
      netOutlay: 8500,
      netSurrenderValue: 57000,
      netDeathBenefit: 285000
    },
    {
      policyYear: 9,
      endOfAge: 48,
      plannedPremium: 10000,
      netOutlay: 9000,
      netSurrenderValue: 58000,
      netDeathBenefit: 290000
    },
    {
      policyYear: 10,
      endOfAge: 49,
      plannedPremium: 10000,
      netOutlay: 9500,
      netSurrenderValue: 59000,
      netDeathBenefit: 295000
    }
  ];

  console.log('✅ Generated table data:', tableData.length, 'rows');
  return tableData;
};

// ===== GENERATE MOCK DATA FOR ALL TABLES =====

export const generateAsIsLoanTableData = (): AsIsLoanTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 2500,
      netValueBeginningOfYear: 12500,
      interestRate: 0.03,
      interestAmount: 375,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 525,
      loanRepayment: 600,
      loanOutstanding: 10425,
      charges: 100,
      netCashValue: 12550
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 2500,
      netValueBeginningOfYear: 15050,
      interestRate: 0.03,
      interestAmount: 451.5,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600,
      loanOutstanding: 10346.25,
      charges: 100,
      netCashValue: 15174.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 2500,
      netValueBeginningOfYear: 17674.25,
      interestRate: 0.03,
      interestAmount: 530.23,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600,
      loanOutstanding: 10263.56,
      charges: 100,
      netCashValue: 17875.17
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 2500,
      netValueBeginningOfYear: 20375.17,
      interestRate: 0.03,
      interestAmount: 611.25,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600,
      loanOutstanding: 10176.74,
      charges: 100,
      netCashValue: 20655.24
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 2500,
      netValueBeginningOfYear: 23155.24,
      interestRate: 0.03,
      interestAmount: 694.66,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600,
      loanOutstanding: 10085.58,
      charges: 100,
      netCashValue: 23517.06
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 2500,
      netValueBeginningOfYear: 26017.06,
      interestRate: 0.03,
      interestAmount: 780.51,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600,
      loanOutstanding: 9989.86,
      charges: 100,
      netCashValue: 26463.30
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 2500,
      netValueBeginningOfYear: 28963.30,
      interestRate: 0.03,
      interestAmount: 868.90,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600,
      loanOutstanding: 9889.35,
      charges: 100,
      netCashValue: 29496.70
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 2500,
      netValueBeginningOfYear: 31996.70,
      interestRate: 0.03,
      interestAmount: 959.90,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600,
      loanOutstanding: 9783.82,
      charges: 100,
      netCashValue: 32620.13
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 2500,
      netValueBeginningOfYear: 35120.13,
      interestRate: 0.03,
      interestAmount: 1053.60,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600,
      loanOutstanding: 9673.01,
      charges: 100,
      netCashValue: 35836.55
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 2500,
      netValueBeginningOfYear: 38336.55,
      interestRate: 0.03,
      interestAmount: 1150.10,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 483.65,
      loanRepayment: 600,
      loanOutstanding: 9556.66,
      charges: 100,
      netCashValue: 39148.99
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 2500,
      netValueBeginningOfYear: 41648.99,
      interestRate: 0.03,
      interestAmount: 1249.47,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 477.83,
      loanRepayment: 600,
      loanOutstanding: 9434.49,
      charges: 100,
      netCashValue: 42560.63
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 2500,
      netValueBeginningOfYear: 45060.63,
      interestRate: 0.03,
      interestAmount: 1351.82,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 471.72,
      loanRepayment: 600,
      loanOutstanding: 9306.22,
      charges: 100,
      netCashValue: 46074.73
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 2500,
      netValueBeginningOfYear: 48574.73,
      interestRate: 0.03,
      interestAmount: 1457.24,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 465.31,
      loanRepayment: 600,
      loanOutstanding: 9171.53,
      charges: 100,
      netCashValue: 49694.66
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 2500,
      netValueBeginningOfYear: 52194.66,
      interestRate: 0.03,
      interestAmount: 1565.84,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 458.58,
      loanRepayment: 600,
      loanOutstanding: 9030.10,
      charges: 100,
      netCashValue: 53423.92
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 2500,
      netValueBeginningOfYear: 55923.92,
      interestRate: 0.03,
      interestAmount: 1677.72,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 451.51,
      loanRepayment: 600,
      loanOutstanding: 8881.61,
      charges: 100,
      netCashValue: 57266.13
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 2500,
      netValueBeginningOfYear: 59766.13,
      interestRate: 0.03,
      interestAmount: 1792.98,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 444.08,
      loanRepayment: 600,
      loanOutstanding: 8725.69,
      charges: 100,
      netCashValue: 61225.04
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 2500,
      netValueBeginningOfYear: 63725.04,
      interestRate: 0.03,
      interestAmount: 1911.75,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 436.28,
      loanRepayment: 600,
      loanOutstanding: 8561.97,
      charges: 100,
      netCashValue: 65304.50
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 2500,
      netValueBeginningOfYear: 67804.50,
      interestRate: 0.03,
      interestAmount: 2034.14,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 428.10,
      loanRepayment: 600,
      loanOutstanding: 8390.07,
      charges: 100,
      netCashValue: 69508.54
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 2500,
      netValueBeginningOfYear: 72008.54,
      interestRate: 0.03,
      interestAmount: 2160.26,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 419.50,
      loanRepayment: 600,
      loanOutstanding: 8209.57,
      charges: 100,
      netCashValue: 73841.29
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 2500,
      netValueBeginningOfYear: 76341.29,
      interestRate: 0.03,
      interestAmount: 2290.24,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 410.48,
      loanRepayment: 600,
      loanOutstanding: 8020.05,
      charges: 100,
      netCashValue: 78307.05
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 80807.05,
      interestRate: 0.03,
      interestAmount: 2424.21,
      faceAmount: 350000.00,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 401.00,
      loanRepayment: 600.00,
      loanOutstanding: 7821.06,
      charges: 100.00,
      netCashValue: 82910.26
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 85410.26,
      interestRate: 0.03,
      interestAmount: 2562.31,
      faceAmount: 350000.00,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 391.05,
      loanRepayment: 600.00,
      loanOutstanding: 7612.11,
      charges: 100.00,
      netCashValue: 87655.52
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 90155.52,
      interestRate: 0.03,
      interestAmount: 2704.67,
      faceAmount: 350000.00,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 380.61,
      loanRepayment: 600.00,
      loanOutstanding: 7392.71,
      charges: 100.00,
      netCashValue: 92547.58
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 95047.58,
      interestRate: 0.03,
      interestAmount: 2851.43,
      faceAmount: 350000.00,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 369.64,
      loanRepayment: 600.00,
      loanOutstanding: 7162.35,
      charges: 100.00,
      netCashValue: 97591.37
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 100091.37,
      interestRate: 0.03,
      interestAmount: 3002.74,
      faceAmount: 350000.00,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 358.12,
      loanRepayment: 600.00,
      loanOutstanding: 6920.47,
      charges: 100.00,
      netCashValue: 102791.99
    }
  ];
};

export const generateFaceAmountTableData = (): FaceAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 12507.14
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 15007.14,
      interestRate: 0.03,
      interestAmount: 450.21,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 15086.39
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 17586.39,
      interestRate: 0.03,
      interestAmount: 527.59,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 17740.10
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 20240.10,
      interestRate: 0.03,
      interestAmount: 607.20,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.00,
      loanOutstanding: 10176.74,
      charges: 100.00,
      netCashValue: 20470.70
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 22970.70,
      interestRate: 0.03,
      interestAmount: 689.12,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.00,
      loanOutstanding: 10085.58,
      charges: 100.00,
      netCashValue: 23280.70
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 25780.70,
      interestRate: 0.03,
      interestAmount: 773.42,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600.00,
      loanOutstanding: 9989.86,
      charges: 100.00,
      netCashValue: 26172.69
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 28672.69,
      interestRate: 0.03,
      interestAmount: 860.18,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600.00,
      loanOutstanding: 9889.35,
      charges: 100.00,
      netCashValue: 29149.38
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 31649.38,
      interestRate: 0.03,
      interestAmount: 949.48,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600.00,
      loanOutstanding: 9783.82,
      charges: 100.00,
      netCashValue: 32213.54
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 34713.54,
      interestRate: 0.03,
      interestAmount: 1041.41,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600.00,
      loanOutstanding: 9673.01,
      charges: 100.00,
      netCashValue: 35368.04
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 37868.04,
      interestRate: 0.03,
      interestAmount: 1136.04,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 483.65,
      loanRepayment: 600.00,
      loanOutstanding: 9556.66,
      charges: 100.00,
      netCashValue: 38615.86
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 41115.86,
      interestRate: 0.03,
      interestAmount: 1233.48,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 477.83,
      loanRepayment: 600.00,
      loanOutstanding: 9434.49,
      charges: 100.00,
      netCashValue: 41960.07
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 44460.07,
      interestRate: 0.03,
      interestAmount: 1333.80,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 471.72,
      loanRepayment: 600.00,
      loanOutstanding: 9306.22,
      charges: 100.00,
      netCashValue: 45403.87
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 47903.87,
      interestRate: 0.03,
      interestAmount: 1437.12,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 465.31,
      loanRepayment: 600.00,
      loanOutstanding: 9171.53,
      charges: 100.00,
      netCashValue: 48950.53
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 51450.53,
      interestRate: 0.03,
      interestAmount: 1543.52,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 458.58,
      loanRepayment: 600.00,
      loanOutstanding: 9030.10,
      charges: 100.00,
      netCashValue: 52603.47
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 55103.47,
      interestRate: 0.03,
      interestAmount: 1653.10,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 451.51,
      loanRepayment: 600.00,
      loanOutstanding: 8881.61,
      charges: 100.00,
      netCashValue: 56366.21
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 58866.21,
      interestRate: 0.03,
      interestAmount: 1765.99,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 444.08,
      loanRepayment: 600.00,
      loanOutstanding: 8725.69,
      charges: 100.00,
      netCashValue: 60242.40
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 62742.40,
      interestRate: 0.03,
      interestAmount: 1882.27,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 436.28,
      loanRepayment: 600.00,
      loanOutstanding: 8561.97,
      charges: 100.00,
      netCashValue: 64235.82
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 66735.82,
      interestRate: 0.03,
      interestAmount: 2002.07,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 428.10,
      loanRepayment: 600.00,
      loanOutstanding: 8390.07,
      charges: 100.00,
      netCashValue: 68350.36
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 70850.36,
      interestRate: 0.03,
      interestAmount: 2125.51,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 419.50,
      loanRepayment: 600.00,
      loanOutstanding: 8209.57,
      charges: 100.00,
      netCashValue: 72590.09
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 75090.09,
      interestRate: 0.03,
      interestAmount: 2252.70,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 410.48,
      loanRepayment: 600.00,
      loanOutstanding: 8020.05,
      charges: 100.00,
      netCashValue: 76959.17
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 79459.17,
      interestRate: 0.03,
      interestAmount: 2383.78,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 401.00,
      loanRepayment: 600.00,
      loanOutstanding: 7821.06,
      charges: 100.00,
      netCashValue: 81461.94
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 83961.94,
      interestRate: 0.03,
      interestAmount: 2518.86,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 391.05,
      loanRepayment: 600.00,
      loanOutstanding: 7612.11,
      charges: 100.00,
      netCashValue: 86102.89
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 88602.89,
      interestRate: 0.03,
      interestAmount: 2658.09,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 380.61,
      loanRepayment: 600.00,
      loanOutstanding: 7392.71,
      charges: 100.00,
      netCashValue: 90886.65
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 93386.65,
      interestRate: 0.03,
      interestAmount: 2801.60,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 369.64,
      loanRepayment: 600.00,
      loanOutstanding: 7162.35,
      charges: 100.00,
      netCashValue: 95818.05
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 98318.05,
      interestRate: 0.03,
      interestAmount: 2949.54,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 358.12,
      loanRepayment: 600.00,
      loanOutstanding: 6920.47,
      charges: 100.00,
      netCashValue: 100902.04
    }
  ];
};

export const generateFaceAmountVariesByYearTableData = (): FaceAmountVariesByYearTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 35,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 12500.0,
      interestRate: 0.03,
      interestAmount: 375.0,
      faceAmount: 200000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.0,
      loanRepayment: 600.0,
      loanOutstanding: 10425.0,
      charges: 100.0,
      netCashValue: 12580.0
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 36,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 15080.0,
      interestRate: 0.03,
      interestAmount: 452.4,
      faceAmount: 200000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.0,
      loanOutstanding: 10346.25,
      charges: 100.0,
      netCashValue: 15235.15
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 37,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 17735.15,
      interestRate: 0.03,
      interestAmount: 532.05,
      faceAmount: 250000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.0,
      loanOutstanding: 10263.56,
      charges: 100.0,
      netCashValue: 17967.89
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 38,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 20467.89,
      interestRate: 0.03,
      interestAmount: 614.04,
      faceAmount: 300000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.0,
      loanOutstanding: 10176.74,
      charges: 100.0,
      netCashValue: 20780.75
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 39,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 23280.75,
      interestRate: 0.03,
      interestAmount: 698.42,
      faceAmount: 300000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.0,
      loanOutstanding: 10085.58,
      charges: 100.0,
      netCashValue: 23088.35
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 40,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 25588.35,
      interestRate: 0.03,
      interestAmount: 767.65,
      faceAmount: 350000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600.0,
      loanOutstanding: 9989.86,
      charges: 100.0,
      netCashValue: 26022.72
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 41,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 28522.72,
      interestRate: 0.03,
      interestAmount: 855.68,
      faceAmount: 350000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600.0,
      loanOutstanding: 9889.35,
      charges: 100.0,
      netCashValue: 29044.91
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 42,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 31544.91,
      interestRate: 0.03,
      interestAmount: 946.35,
      faceAmount: 400000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600.0,
      loanOutstanding: 9783.82,
      charges: 100.0,
      netCashValue: 32156.79
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 43,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 34656.79,
      interestRate: 0.03,
      interestAmount: 1039.70,
      faceAmount: 400000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600.0,
      loanOutstanding: 9673.01,
      charges: 100.0,
      netCashValue: 35367.30
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 44,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 37867.30,
      interestRate: 0.03,
      interestAmount: 1136.02,
      faceAmount: 450000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 483.65,
      loanRepayment: 600.0,
      loanOutstanding: 9556.66,
      charges: 100.0,
      netCashValue: 38669.67
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 45,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 41169.67,
      interestRate: 0.03,
      interestAmount: 1235.09,
      faceAmount: 450000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 477.83,
      loanRepayment: 600.0,
      loanOutstanding: 9434.49,
      charges: 100.0,
      netCashValue: 42070.43
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 46,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 44570.43,
      interestRate: 0.03,
      interestAmount: 1337.11,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 471.72,
      loanRepayment: 600.0,
      loanOutstanding: 9306.22,
      charges: 100.0,
      netCashValue: 45578.82
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 47,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 48078.82,
      interestRate: 0.03,
      interestAmount: 1442.36,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 465.31,
      loanRepayment: 600.0,
      loanOutstanding: 9171.53,
      charges: 100.0,
      netCashValue: 49186.87
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 48,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 51686.87,
      interestRate: 0.03,
      interestAmount: 1550.61,
      faceAmount: 550000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 458.58,
      loanRepayment: 600.0,
      loanOutstanding: 9030.10,
      charges: 100.0,
      netCashValue: 52895.90
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 49,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 55395.90,
      interestRate: 0.03,
      interestAmount: 1661.88,
      faceAmount: 550000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 451.51,
      loanRepayment: 600.0,
      loanOutstanding: 8881.61,
      charges: 100.0,
      netCashValue: 56720.27
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 50,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 59220.27,
      interestRate: 0.03,
      interestAmount: 1776.61,
      faceAmount: 600000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 444.08,
      loanRepayment: 600.0,
      loanOutstanding: 8725.69,
      charges: 100.0,
      netCashValue: 60652.80
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 51,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 63152.80,
      interestRate: 0.03,
      interestAmount: 1894.58,
      faceAmount: 600000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 436.28,
      loanRepayment: 600.0,
      loanOutstanding: 8561.97,
      charges: 100.0,
      netCashValue: 64710.11
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 52,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 67210.11,
      interestRate: 0.03,
      interestAmount: 2016.30,
      faceAmount: 650000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 428.10,
      loanRepayment: 600.0,
      loanOutstanding: 8390.07,
      charges: 100.0,
      netCashValue: 68898.31
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 53,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 71398.31,
      interestRate: 0.03,
      interestAmount: 2141.95,
      faceAmount: 650000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 419.50,
      loanRepayment: 600.0,
      loanOutstanding: 8209.57,
      charges: 100.0,
      netCashValue: 73220.69
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 54,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 75720.69,
      interestRate: 0.03,
      interestAmount: 2271.62,
      faceAmount: 700000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 410.48,
      loanRepayment: 600.0,
      loanOutstanding: 8020.05,
      charges: 100.0,
      netCashValue: 77681.83
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 55,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 80181.83,
      interestRate: 0.03,
      interestAmount: 2405.45,
      faceAmount: 700000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 401.00,
      loanRepayment: 600.0,
      loanOutstanding: 7821.06,
      charges: 100.0,
      netCashValue: 82286.28
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 56,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 84786.28,
      interestRate: 0.03,
      interestAmount: 2543.59,
      faceAmount: 750000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 391.05,
      loanRepayment: 600.0,
      loanOutstanding: 7612.11,
      charges: 100.0,
      netCashValue: 87029.82
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 57,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 89529.82,
      interestRate: 0.03,
      interestAmount: 2685.89,
      faceAmount: 750000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 380.61,
      loanRepayment: 600.0,
      loanOutstanding: 7392.71,
      charges: 100.0,
      netCashValue: 91915.10
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 58,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 94415.10,
      interestRate: 0.03,
      interestAmount: 2832.45,
      faceAmount: 800000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 369.64,
      loanRepayment: 600.0,
      loanOutstanding: 7162.35,
      charges: 100.0,
      netCashValue: 96947.91
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 59,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 99447.91,
      interestRate: 0.03,
      interestAmount: 2983.44,
      faceAmount: 800000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 358.12,
      loanRepayment: 600.0,
      loanOutstanding: 6920.47,
      charges: 100.0,
      netCashValue: 102131.23
    }
  ];
};

export const generatePremiumAmountTableData = (): PremiumAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 13550.00
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 17050.00,
      interestRate: 0.03,
      interestAmount: 511.50,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 17234.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 20734.25,
      interestRate: 0.03,
      interestAmount: 622.03,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 20938.97
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 24438.97,
      interestRate: 0.03,
      interestAmount: 733.17,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.00,
      loanOutstanding: 10176.74,
      charges: 100.00,
      netCashValue: 24755.96
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 28255.96,
      interestRate: 0.03,
      interestAmount: 847.68,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.00,
      loanOutstanding: 10085.58,
      charges: 100.00,
      netCashValue: 28695.80
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 32195.80,
      interestRate: 0.03,
      interestAmount: 965.87,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600.00,
      loanOutstanding: 9989.86,
      charges: 100.00,
      netCashValue: 32757.39
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 36257.39,
      interestRate: 0.03,
      interestAmount: 1087.72,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600.00,
      loanOutstanding: 9889.35,
      charges: 100.00,
      netCashValue: 36944.62
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 40444.62,
      interestRate: 0.03,
      interestAmount: 1213.34,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600.00,
      loanOutstanding: 9783.82,
      charges: 100.00,
      netCashValue: 41263.49
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 44763.49,
      interestRate: 0.03,
      interestAmount: 1342.90,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600.00,
      loanOutstanding: 9673.01,
      charges: 100.00,
      netCashValue: 45717.20
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 49217.20,
      interestRate: 0.03,
      interestAmount: 1476.52,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 483.65,
      loanRepayment: 600.00,
      loanOutstanding: 9556.66,
      charges: 100.00,
      netCashValue: 50310.07
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 53810.07,
      interestRate: 0.03,
      interestAmount: 1614.30,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 477.83,
      loanRepayment: 600.00,
      loanOutstanding: 9434.49,
      charges: 100.00,
      netCashValue: 55046.54
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 58546.54,
      interestRate: 0.03,
      interestAmount: 1756.40,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 471.72,
      loanRepayment: 600.00,
      loanOutstanding: 9306.22,
      charges: 100.00,
      netCashValue: 59931.22
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 63431.22,
      interestRate: 0.03,
      interestAmount: 1902.94,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 465.31,
      loanRepayment: 600.00,
      loanOutstanding: 9171.53,
      charges: 100.00,
      netCashValue: 64968.85
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 68468.85,
      interestRate: 0.03,
      interestAmount: 2054.07,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 458.58,
      loanRepayment: 600.00,
      loanOutstanding: 9030.10,
      charges: 100.00,
      netCashValue: 70164.34
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 73664.34,
      interestRate: 0.03,
      interestAmount: 2209.93,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 451.51,
      loanRepayment: 600.00,
      loanOutstanding: 8881.61,
      charges: 100.00,
      netCashValue: 75522.76
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 79022.76,
      interestRate: 0.03,
      interestAmount: 2370.68,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 444.08,
      loanRepayment: 600.00,
      loanOutstanding: 8725.69,
      charges: 100.00,
      netCashValue: 81049.36
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 84549.36,
      interestRate: 0.03,
      interestAmount: 2536.48,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 436.28,
      loanRepayment: 600.00,
      loanOutstanding: 8561.97,
      charges: 100.00,
      netCashValue: 86749.56
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 90249.56,
      interestRate: 0.03,
      interestAmount: 2707.49,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 428.10,
      loanRepayment: 600.00,
      loanOutstanding: 8390.07,
      charges: 100.00,
      netCashValue: 92628.95
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 96128.95,
      interestRate: 0.03,
      interestAmount: 2883.87,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 419.50,
      loanRepayment: 600.00,
      loanOutstanding: 8209.57,
      charges: 100.00,
      netCashValue: 98693.25
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 102193.25,
      interestRate: 0.03,
      interestAmount: 3065.80,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 410.48,
      loanRepayment: 600.00,
      loanOutstanding: 8020.05,
      charges: 100.00,
      netCashValue: 104948.57
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 108448.57,
      interestRate: 0.03,
      interestAmount: 3253.46,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 401.00,
      loanRepayment: 600.00,
      loanOutstanding: 7821.06,
      charges: 100.00,
      netCashValue: 111401.03
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 114901.03,
      interestRate: 0.03,
      interestAmount: 3447.03,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 391.05,
      loanRepayment: 600.00,
      loanOutstanding: 7612.11,
      charges: 100.00,
      netCashValue: 118057.01
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 121557.01,
      interestRate: 0.03,
      interestAmount: 3646.71,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 380.61,
      loanRepayment: 600.00,
      loanOutstanding: 7392.71,
      charges: 100.00,
      netCashValue: 124923.11
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 128423.11,
      interestRate: 0.03,
      interestAmount: 3852.69,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 369.64,
      loanRepayment: 600.00,
      loanOutstanding: 7162.35,
      charges: 100.00,
      netCashValue: 132006.16
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 3500.00,
      netValueBeginningOfYear: 135506.16,
      interestRate: 0.03,
      interestAmount: 4065.18,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 358.12,
      loanRepayment: 600.00,
      loanOutstanding: 6920.47,
      charges: 100.00,
      netCashValue: 139313.22
    }
  ];
};

export const generateStopPremiumAmountTableData = (): StopPremiumAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 11550.00
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 11550.00,
      interestRate: 0.03,
      interestAmount: 346.50,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 10975.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 10975.25,
      interestRate: 0.03,
      interestAmount: 329.26,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 10387.20
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 10387.20,
      interestRate: 0.03,
      interestAmount: 311.62,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.00,
      loanOutstanding: 10176.74,
      charges: 100.00,
      netCashValue: 9785.64
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 9785.64,
      interestRate: 0.03,
      interestAmount: 293.57,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.00,
      loanOutstanding: 10085.58,
      charges: 100.00,
      netCashValue: 9170.37
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 9170.37,
      interestRate: 0.03,
      interestAmount: 275.11,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600.00,
      loanOutstanding: 9989.86,
      charges: 100.00,
      netCashValue: 8541.20
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 8541.20,
      interestRate: 0.03,
      interestAmount: 256.24,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600.00,
      loanOutstanding: 9889.35,
      charges: 100.00,
      netCashValue: 7897.95
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 7897.95,
      interestRate: 0.03,
      interestAmount: 236.94,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600.00,
      loanOutstanding: 9783.82,
      charges: 100.00,
      netCashValue: 7240.42
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 7240.42,
      interestRate: 0.03,
      interestAmount: 217.21,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600.00,
      loanOutstanding: 9673.01,
      charges: 100.00,
      netCashValue: 6568.44
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 6568.44,
      interestRate: 0.03,
      interestAmount: 197.05,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 483.65,
      loanRepayment: 600.00,
      loanOutstanding: 9556.66,
      charges: 100.00,
      netCashValue: 5881.84
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 5881.84,
      interestRate: 0.03,
      interestAmount: 176.46,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 477.83,
      loanRepayment: 600.00,
      loanOutstanding: 9434.49,
      charges: 100.00,
      netCashValue: 5180.47
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 5180.47,
      interestRate: 0.03,
      interestAmount: 155.41,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 471.72,
      loanRepayment: 600.00,
      loanOutstanding: 9306.22,
      charges: 100.00,
      netCashValue: 4464.16
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 4464.16,
      interestRate: 0.03,
      interestAmount: 133.92,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 465.31,
      loanRepayment: 600.00,
      loanOutstanding: 9171.53,
      charges: 100.00,
      netCashValue: 3732.77
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 3732.77,
      interestRate: 0.03,
      interestAmount: 111.98,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 458.58,
      loanRepayment: 600.00,
      loanOutstanding: 9030.10,
      charges: 100.00,
      netCashValue: 2986.17
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 2986.17,
      interestRate: 0.03,
      interestAmount: 89.59,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 451.51,
      loanRepayment: 600.00,
      loanOutstanding: 8881.61,
      charges: 100.00,
      netCashValue: 2224.25
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 2224.25,
      interestRate: 0.03,
      interestAmount: 66.73,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 444.08,
      loanRepayment: 600.00,
      loanOutstanding: 8725.69,
      charges: 100.00,
      netCashValue: 1446.90
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 1446.90,
      interestRate: 0.03,
      interestAmount: 43.41,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 436.28,
      loanRepayment: 600.00,
      loanOutstanding: 8561.97,
      charges: 100.00,
      netCashValue: 654.03
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 654.03,
      interestRate: 0.03,
      interestAmount: 19.62,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 428.10,
      loanRepayment: 600.00,
      loanOutstanding: 8390.07,
      charges: 100.00,
      netCashValue: -154.45
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 0.00,
      netValueBeginningOfYear: -154.45,
      interestRate: 0.03,
      interestAmount: -4.63,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 419.50,
      loanRepayment: 600.00,
      loanOutstanding: 8209.57,
      charges: 100.00,
      netCashValue: -978.58
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 0.00,
      netValueBeginningOfYear: -978.58,
      interestRate: 0.03,
      interestAmount: -29.36,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 410.48,
      loanRepayment: 600.00,
      loanOutstanding: 8020.05,
      charges: 100.00,
      netCashValue: -1818.42
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 0.00,
      netValueBeginningOfYear: -1818.42,
      interestRate: 0.03,
      interestAmount: -54.55,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 401.00,
      loanRepayment: 600.00,
      loanOutstanding: 7821.06,
      charges: 100.00,
      netCashValue: -2672.97
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 0.00,
      netValueBeginningOfYear: -2672.97,
      interestRate: 0.03,
      interestAmount: -80.19,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 391.05,
      loanRepayment: 600.00,
      loanOutstanding: 7612.11,
      charges: 100.00,
      netCashValue: -3544.21
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 0.00,
      netValueBeginningOfYear: -3544.21,
      interestRate: 0.03,
      interestAmount: -106.33,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 380.61,
      loanRepayment: 600.00,
      loanOutstanding: 7392.71,
      charges: 100.00,
      netCashValue: -4431.15
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 0.00,
      netValueBeginningOfYear: -4431.15,
      interestRate: 0.03,
      interestAmount: -132.93,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 369.64,
      loanRepayment: 600.00,
      loanOutstanding: 7162.35,
      charges: 100.00,
      netCashValue: -5333.72
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 0.00,
      netValueBeginningOfYear: -5333.72,
      interestRate: 0.03,
      interestAmount: -160.01,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 358.12,
      loanRepayment: 600.00,
      loanOutstanding: 6920.47,
      charges: 100.00,
      netCashValue: -6251.85
    }
  ];
};
