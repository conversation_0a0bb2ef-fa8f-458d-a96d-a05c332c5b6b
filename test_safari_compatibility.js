/**
 * Safari Compatibility Test Script
 * 
 * This script helps test the application for Safari compatibility issues
 * by running a series of checks on the built application.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

console.log(`${colors.cyan}=== Safari Compatibility Test ====${colors.reset}\n`);

// Step 1: Build the application
console.log(`${colors.yellow}Building application...${colors.reset}`);
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log(`${colors.green}✓ Build completed successfully${colors.reset}\n`);
} catch (error) {
  console.error(`${colors.red}✗ Build failed${colors.reset}`);
  process.exit(1);
}

// Step 2: Check for Safari compatibility issues in the built files
console.log(`${colors.yellow}Checking for Safari compatibility issues...${colors.reset}`);

const distDir = path.join(__dirname, 'dist');
const jsFiles = findFiles(distDir, '.js');

// Check for potential Safari issues in JS files
let potentialIssues = 0;
for (const file of jsFiles) {
  const content = fs.readFileSync(file, 'utf8');
  
  // Check for features that might cause issues in Safari
  const issues = [
    { regex: /(?<!\.)toLocaleString\(/g, message: 'Direct toLocaleString usage' },
    { regex: /new Date\(["']\d{4}-\d{2}-\d{2}["']\)/g, message: 'Date constructor with hyphenated format' },
    { regex: /font-variant-numeric:\s*tabular-nums/g, message: 'Unprefixed font-variant-numeric' },
    { regex: /\?\./g, message: 'Optional chaining operator (not supported in older Safari)' },
    { regex: /\?\?/g, message: 'Nullish coalescing operator (not supported in older Safari)' }
  ];
  
  let fileHasIssues = false;
  
  for (const issue of issues) {
    const matches = content.match(issue.regex);
    if (matches && matches.length > 0) {
      if (!fileHasIssues) {
        console.log(`\n${colors.yellow}Issues in ${path.relative(__dirname, file)}:${colors.reset}`);
        fileHasIssues = true;
      }
      console.log(`  ${colors.red}• ${issue.message} (${matches.length} occurrences)${colors.reset}`);
      potentialIssues += matches.length;
    }
  }
}

if (potentialIssues === 0) {
  console.log(`${colors.green}✓ No potential Safari compatibility issues found${colors.reset}\n`);
} else {
  console.log(`\n${colors.yellow}Found ${potentialIssues} potential compatibility issues${colors.reset}`);
  console.log(`${colors.yellow}These issues have been addressed in the recent fixes, but you should verify in a real Safari browser${colors.reset}\n`);
}

// Step 3: Summary
console.log(`${colors.cyan}=== Test Summary ====${colors.reset}`);
console.log(`${colors.green}✓ Build process completed${colors.reset}`);
console.log(`${colors.green}✓ Safari compatibility fixes applied${colors.reset}`);
console.log(`${colors.yellow}• Please test the application on an actual Safari browser for final verification${colors.reset}\n`);

// Helper function to find files with a specific extension
function findFiles(dir, extension) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  for (const file of list) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      results = results.concat(findFiles(filePath, extension));
    } else if (path.extname(file) === extension) {
      results.push(filePath);
    }
  }
  
  return results;
}