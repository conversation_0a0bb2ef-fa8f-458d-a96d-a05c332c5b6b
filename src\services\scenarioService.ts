// API Base URL - loaded from .env file
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Types for scenario management - Updated to match backend ScenarioSummary exactly
export interface ScenarioSummary {
  scenario_id: number;
  policy_id: number;
  illustration_id: number;
  date_of_illustration: string; // Backend returns date as string
  illustration_type_id: number;
  illustration_type_description?: string;
  illustration_question_id: number;
  illustration_question_description?: string;
  illustration_option_id?: number | null;
  illustration_option_description?: string | null;
  illustration_starting_age?: number | null;
  illustration_ending_age?: number | null;
  new_face_amount?: number | null;
  new_coverage_option?: string | null;
  new_premium_amount?: number | null;
  new_loan_amount?: number | null;
  new_loan_repayment_amount?: number | null;
  current_interest_rate?: number | null;
  guaranteed_minimum_rate?: number | null;
  illustration_interest_rate?: number | null;
  surrender_amount?: number | null;
  retirement_age_goal?: number | null;
  is_schedule?: 'YES' | 'NO';
}

export interface GetPolicyScenariosResponse {
  policy_id: number;
  scenarios: ScenarioSummary[];
  total_count: number;
}

export interface ScenarioApiResponse {
  status: 'SUCCESS' | 'FAILED';
  message?: string;
  scenario_id?: number;
}

// Map illustration type IDs to category names
const ILLUSTRATION_TYPE_CATEGORY_MAP: Record<number, string> = {
  1: 'as-is-saved', // Changed from 'as-is' to 'as-is-saved' so it's not filtered out
  2: 'face-amount',
  3: 'premium',
  4: 'interest-rate', // Fixed: Type 4 is Interest Rate
  5: 'income',        // Fixed: Type 5 is Income (Full Surrender/Income)
  6: 'loan-repayment' // Fixed: Type 6 is Loan Repayment
};

// Map category names to display labels
const CATEGORY_LABELS: Record<string, string> = {
  'as-is': 'AS-IS',
  'as-is-saved': 'AS-IS', // Same display label as 'as-is'
  'face-amount': 'Face Amount',
  'premium': 'Premium',
  'interest-rate': 'Interest Rate',
  'income': 'Full Surrender / Income',
  'loan-repayment': 'Loan Repayment',
};

/**
 * Convert backend scenario data to frontend Scenario format
 * Updated to match exact backend database structure
 */
export function convertBackendScenarioToFrontend(backendScenario: ScenarioSummary): any {
  const category = ILLUSTRATION_TYPE_CATEGORY_MAP[backendScenario.illustration_type_id] || 'unknown';

  // Helper function to safely format numbers
  const formatNumber = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return 'N/A';
    }
    return value.toLocaleString();
  };

  // Helper function to safely format currency
  const formatCurrency = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return 'N/A';
    }
    return `$${value.toLocaleString()}`;
  };

  // Helper function to safely format percentage
  const formatPercentage = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(value)) {
      return 'N/A';
    }
    return `${(value * 100).toFixed(2)}%`;
  };

  // Helper function to safely format age range
  const formatAgeRange = (startAge: number | null | undefined, endAge: number | null | undefined): string => {
    const start = startAge && !isNaN(startAge) ? startAge.toString() : 'N/A';
    const end = endAge && !isNaN(endAge) ? endAge.toString() : 'N/A';
    return `${start} - ${end}`;
  };

  // Build comprehensive key points from all backend data
  const keyPoints = [
    // Core scenario information
    `Scenario ID: ${backendScenario.scenario_id}`,
    `Illustration Type: ${backendScenario.illustration_type_description || 'Unknown Type'}`,
    `Question: ${backendScenario.illustration_question_description || 'No question specified'}`,

    // Option information
    backendScenario.illustration_option_description ? `Option: ${backendScenario.illustration_option_description}` : '',

    // Age range
    (backendScenario.illustration_starting_age || backendScenario.illustration_ending_age) ?
      `Age Range: ${formatAgeRange(backendScenario.illustration_starting_age, backendScenario.illustration_ending_age)}` : '',

    // Financial values - Face Amount
    (backendScenario.new_face_amount && backendScenario.new_face_amount > 0) ?
      `New Face Amount: ${formatCurrency(backendScenario.new_face_amount)}` : '',

    // Coverage option
    backendScenario.new_coverage_option ?
      `New Coverage Option: ${backendScenario.new_coverage_option}` : '',

    // Premium information
    (backendScenario.new_premium_amount && backendScenario.new_premium_amount > 0) ?
      `New Premium Amount: ${formatCurrency(backendScenario.new_premium_amount)}` : '',

    // Loan information
    (backendScenario.new_loan_amount && backendScenario.new_loan_amount > 0) ?
      `New Loan Amount: ${formatCurrency(backendScenario.new_loan_amount)}` : '',
    (backendScenario.new_loan_repayment_amount && backendScenario.new_loan_repayment_amount > 0) ?
      `Loan Repayment Amount: ${formatCurrency(backendScenario.new_loan_repayment_amount)}` : '',

    // Interest rates
    (backendScenario.current_interest_rate && backendScenario.current_interest_rate > 0) ?
      `Current Interest Rate: ${formatPercentage(backendScenario.current_interest_rate)}` : '',
    (backendScenario.guaranteed_minimum_rate && backendScenario.guaranteed_minimum_rate > 0) ?
      `Guaranteed Minimum Rate: ${formatPercentage(backendScenario.guaranteed_minimum_rate)}` : '',
    (backendScenario.illustration_interest_rate && backendScenario.illustration_interest_rate > 0) ?
      `Illustration Interest Rate: ${formatPercentage(backendScenario.illustration_interest_rate)}` : '',

    // Surrender and retirement
    (backendScenario.surrender_amount && backendScenario.surrender_amount > 0) ?
      `Surrender Amount: ${formatCurrency(backendScenario.surrender_amount)}` : '',
    backendScenario.retirement_age_goal ?
      `Retirement Age Goal: ${backendScenario.retirement_age_goal}` : '',

    // Schedule information
    backendScenario.is_schedule ?
      `Schedule: ${backendScenario.is_schedule}` : '',

    // Date information
    `Date of Illustration: ${new Date(backendScenario.date_of_illustration).toLocaleDateString()}`
  ].filter(Boolean);

  console.log('🔄 Converting backend scenario:', {
    scenario_id: backendScenario.scenario_id,
    policy_id: backendScenario.policy_id,
    illustration_id: backendScenario.illustration_id,
    question: backendScenario.illustration_question_description,
    option: backendScenario.illustration_option_description,
    face_amount: backendScenario.new_face_amount,
    premium_amount: backendScenario.new_premium_amount,
    starting_age: backendScenario.illustration_starting_age,
    ending_age: backendScenario.illustration_ending_age
  });

  return {
    id: backendScenario.scenario_id.toString(),
    name: `${backendScenario.illustration_type_description || 'Unknown Type'}`,
    policyId: backendScenario.policy_id.toString(),
    asIsDetails: backendScenario.illustration_question_description || 'No question specified',
    whatIfOptions: backendScenario.illustration_option_description ? [backendScenario.illustration_option_description] : [],
    category: category,
    keyPoints: keyPoints,
    data: {
      // Store complete backend data for direct access (this is the key!)
      backendData: backendScenario,
      timestamp: backendScenario.date_of_illustration,
      // Additional formatted data for easy access
      formattedData: {
        scenarioId: backendScenario.scenario_id,
        policyId: backendScenario.policy_id,
        illustrationId: backendScenario.illustration_id,
        dateOfIllustration: new Date(backendScenario.date_of_illustration),
        typeDescription: backendScenario.illustration_type_description,
        questionDescription: backendScenario.illustration_question_description,
        optionDescription: backendScenario.illustration_option_description,
        ageRange: formatAgeRange(backendScenario.illustration_starting_age, backendScenario.illustration_ending_age),
        newFaceAmount: formatCurrency(backendScenario.new_face_amount),
        newPremiumAmount: formatCurrency(backendScenario.new_premium_amount),
        currentInterestRate: formatPercentage(backendScenario.current_interest_rate),
        guaranteedMinimumRate: formatPercentage(backendScenario.guaranteed_minimum_rate),
        illustrationInterestRate: formatPercentage(backendScenario.illustration_interest_rate)
      }
    },
    createdAt: new Date(backendScenario.date_of_illustration),
    updatedAt: new Date(backendScenario.date_of_illustration),
  };
}

/**
 * Fetch all scenarios for a specific policy from the backend
 */
export async function fetchPolicyScenarios(policyId: number): Promise<any[]> {
  try {
    const apiUrl = `${API_BASE_URL}/get_policy_scenarios?policy_id=${policyId}`;
    console.log('🔥🔥🔥 MAKING ACTUAL DATABASE API CALL 🔥🔥🔥');
    console.log('🔍 Policy ID:', policyId);
    console.log('🔍 API URL:', apiUrl);
    console.log('🔍 API_BASE_URL:', API_BASE_URL);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response ok:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);
      throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
    }

    const data: GetPolicyScenariosResponse = await response.json();
    console.log('🔥🔥🔥 RAW DATABASE RESPONSE 🔥🔥🔥');
    console.log('📊 Full backend response:', JSON.stringify(data, null, 2));
    console.log('📊 Number of scenarios from DB:', data.scenarios?.length || 0);

    if (!data.scenarios || data.scenarios.length === 0) {
      console.log('⚠️ NO SCENARIOS FOUND IN DATABASE for policy:', policyId);
      return [];
    }

    // Log each scenario's raw data before conversion
    console.log('🔥 RAW SCENARIO DATA FROM DATABASE:');
    data.scenarios.forEach((scenario, index) => {
      console.log(`📋 Raw Scenario ${index + 1}:`, {
        scenario_id: scenario.scenario_id,
        policy_id: scenario.policy_id,
        illustration_id: scenario.illustration_id,
        illustration_type_id: scenario.illustration_type_id,
        illustration_type_description: scenario.illustration_type_description,
        illustration_question_id: scenario.illustration_question_id,
        illustration_question_description: scenario.illustration_question_description,
        illustration_option_id: scenario.illustration_option_id,
        illustration_option_description: scenario.illustration_option_description,
        new_face_amount: scenario.new_face_amount,
        new_premium_amount: scenario.new_premium_amount,
        illustration_starting_age: scenario.illustration_starting_age,
        illustration_ending_age: scenario.illustration_ending_age,
        retirement_age_goal: scenario.retirement_age_goal,
        new_loan_amount: scenario.new_loan_amount,
        new_loan_repayment_amount: scenario.new_loan_repayment_amount,
        current_interest_rate: scenario.current_interest_rate,
        guaranteed_minimum_rate: scenario.guaranteed_minimum_rate,
        illustration_interest_rate: scenario.illustration_interest_rate,
        surrender_amount: scenario.surrender_amount,
        new_coverage_option: scenario.new_coverage_option,
        is_schedule: scenario.is_schedule,
        date_of_illustration: scenario.date_of_illustration
      });
    });

    // Convert backend scenarios to frontend format
    const frontendScenarios = data.scenarios.map(convertBackendScenarioToFrontend);

    console.log('🔥🔥🔥 CONVERTED DATABASE SCENARIOS 🔥🔥🔥');
    console.log('✅ Converted scenarios count:', frontendScenarios.length);
    console.log('✅ Converted scenarios:', frontendScenarios);
    console.log('🔍 Scenario IDs from database:', data.scenarios.map(s => s.scenario_id));
    console.log('🔍 Policy IDs from database:', data.scenarios.map(s => s.policy_id));

    return frontendScenarios;

  } catch (error) {
    console.error('❌❌❌ DATABASE FETCH ERROR ❌❌❌');
    console.error('❌ Error fetching policy scenarios:', error);
    throw new Error(`Failed to fetch scenarios: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Check if a scenario was successfully saved by verifying it exists in the backend
 */
export async function verifyScenarioSaved(policyId: number, expectedCount?: number): Promise<boolean> {
  try {
    const scenarios = await fetchPolicyScenarios(policyId);
    
    if (expectedCount !== undefined) {
      return scenarios.length >= expectedCount;
    }
    
    return scenarios.length > 0;
  } catch (error) {
    console.error('❌ Error verifying scenario save:', error);
    return false;
  }
}

/**
 * Get the category label for display
 */
export function getCategoryLabel(category: string): string {
  return CATEGORY_LABELS[category] || category;
}

/**
 * Get scenarios grouped by category
 */
export function groupScenariosByCategory(scenarios: any[]): Record<string, any[]> {
  const grouped: Record<string, any[]> = {};

  scenarios.forEach(scenario => {
    const category = scenario.category;
    if (!grouped[category]) {
      grouped[category] = [];
    }
    grouped[category].push(scenario);
  });

  return grouped;
}

// ===== TABLE DATA TYPES =====

export interface ScenarioTableData {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

// ===== CHART DATA SERVICES =====

export interface ChartDataPoint {
  year: number;
  age: number;
  'Planned Premium': number;
  'Net Outlay': number;
  'Net Surrender Value': number;
  'Net Death Benefit': number;
}

export interface PieChartDataPoint {
  name: string;
  value: number;
  color: string;
}

/**
 * Prepare chart data for visualization from table data
 * @param tableData - The scenario table data
 * @returns Chart data formatted for recharts
 */
export function prepareChartData(tableData: ScenarioTableData[]): ChartDataPoint[] {
  return tableData.map(row => ({
    year: row.policyYear,
    age: row.endOfAge,
    'Planned Premium': row.plannedPremium,
    'Net Outlay': row.netOutlay,
    'Net Surrender Value': row.netSurrenderValue,
    'Net Death Benefit': row.netDeathBenefit
  }));
}

/**
 * Prepare pie chart data from the latest year data
 * @param tableData - The scenario table data
 * @param chartColors - Color configuration for charts
 * @returns Pie chart data formatted for recharts
 */
export function preparePieChartData(tableData: ScenarioTableData[], chartColors: any): PieChartDataPoint[] {
  if (tableData.length === 0) return [];

  const latestData = tableData[tableData.length - 1];
  return [
    { name: 'Planned Premium', value: latestData.plannedPremium, color: chartColors.plannedPremium },
    { name: 'Net Outlay', value: latestData.netOutlay, color: chartColors.netOutlay },
    { name: 'Net Surrender Value', value: latestData.netSurrenderValue, color: chartColors.netSurrenderValue },
    { name: 'Net Death Benefit', value: latestData.netDeathBenefit, color: chartColors.netDeathBenefit }
  ];
}

// ===== KEY METRICS CALCULATION SERVICES =====

export interface KeyMetrics {
  totalPremiums: number;
  totalOutlay: number;
  finalSurrenderValue: number;
  finalDeathBenefit: number;
  netGain: number;
  roi: number;
}

/**
 * Calculate key performance metrics from table data
 * @param tableData - The scenario table data
 * @returns Calculated key metrics
 */
export function calculateKeyMetrics(tableData: ScenarioTableData[]): KeyMetrics {
  if (tableData.length === 0) {
    return {
      totalPremiums: 0,
      totalOutlay: 0,
      finalSurrenderValue: 0,
      finalDeathBenefit: 0,
      netGain: 0,
      roi: 0
    };
  }

  const lastYear = tableData[tableData.length - 1];
  const totalPremiums = tableData.reduce((sum: number, row: ScenarioTableData) => sum + row.plannedPremium, 0);
  const totalOutlay = tableData.reduce((sum: number, row: ScenarioTableData) => sum + row.netOutlay, 0);
  const finalSurrenderValue = lastYear.netSurrenderValue;
  const finalDeathBenefit = lastYear.netDeathBenefit;
  const netGain = finalSurrenderValue - totalOutlay;
  const roi = totalOutlay > 0 ? ((netGain / totalOutlay) * 100) : 0;

  return {
    totalPremiums,
    totalOutlay,
    finalSurrenderValue,
    finalDeathBenefit,
    netGain,
    roi
  };
}


