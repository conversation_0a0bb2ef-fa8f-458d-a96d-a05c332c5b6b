/**
 * AI Commentary Service - Backend API Integration
 * 
 * This service handles API calls to the LLM commentary backend
 * for generating AI-powered policy analysis and recommendations.
 */

// LLM Commentary API Base URL - using separate port for LLM service
const LLM_API_BASE_URL = 'http://localhost:5000';

/**
 * Interface for scenario data to be sent to LLM service
 */
export interface ScenarioData {
  policyYear: number;
  age: number;
  plannedPremium: string;
  netOutlay: string;
  netSurrenderValue: string;
  netDeathBenefit: string;
}

/**
 * Interface for policy information
 */
export interface PolicyInfo {
  policyNumber?: string;
  customerName?: string;
  policyType?: string;
  faceAmount?: string;
  annualPremium?: string;
  currentAge?: string;
  retirementAge?: string;
}

/**
 * Interface for commentary request
 */
export interface CommentaryRequest {
  policyInfo?: PolicyInfo;
  scenarioData: ScenarioData[];
  scenarioName?: string;
  scenarioCategory?: string;
  additionalContext?: string;
}

/**
 * Interface for commentary response from backend
 */
export interface CommentaryResponse {
  response: {
    raw: string;
    formatted: string;
  };
}

/**
 * Interface for processed commentary sections
 */
export interface ProcessedCommentary {
  summary: string;
  recommendations: string[];
  risks: string[];
  keyInsights: string[];
}

/**
 * Fetch AI commentary from the LLM backend service
 *
 * @param requestData - The commentary request data
 * @returns Promise with commentary response
 */
export const fetchAICommentary = async (requestData: CommentaryRequest): Promise<CommentaryResponse> => {
  try {
    console.log('🔍 Fetching AI commentary from:', `${LLM_API_BASE_URL}/api/commentary`);
    console.log('📋 Request data:', requestData);

    // Convert request data to URL parameters for GET request
    const params = new URLSearchParams();

    // Add policy info as parameters
    if (requestData.policyInfo?.policyNumber) {
      params.append('policyNumber', requestData.policyInfo.policyNumber);
    }
    if (requestData.policyInfo?.customerName) {
      params.append('customerName', requestData.policyInfo.customerName);
    }
    if (requestData.policyInfo?.policyType) {
      params.append('policyType', requestData.policyInfo.policyType);
    }
    if (requestData.scenarioName) {
      params.append('scenarioName', requestData.scenarioName);
    }
    if (requestData.scenarioCategory) {
      params.append('scenarioCategory', requestData.scenarioCategory);
    }

    // Add scenario data as JSON parameter
    params.append('scenarioData', JSON.stringify(requestData.scenarioData));

    const url = `${LLM_API_BASE_URL}/api/commentary?${params.toString()}`;
    console.log('🔗 Request URL:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    console.log('📡 Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error Response:', errorText);

      // Provide specific error messages based on status code
      if (response.status === 404) {
        throw new Error('AI Commentary service endpoint not found. Please check if the backend service is running.');
      } else if (response.status === 500) {
        throw new Error('AI Commentary service encountered an internal error. Please try again later.');
      } else if (response.status === 0 || !response.status) {
        throw new Error('Cannot connect to AI Commentary service. Please ensure the backend service is running on port 5000.');
      } else {
        throw new Error(`AI Commentary service error: ${response.status} - ${response.statusText}`);
      }
    }

    const data: CommentaryResponse = await response.json();
    console.log('✅ Commentary received:', data);

    // Validate that we received actual data from backend
    if (!data || !data.response || !data.response.formatted) {
      throw new Error('Invalid response format from AI Commentary service.');
    }

    return data;
  } catch (error) {
    console.error('❌ Error fetching AI commentary:', error);

    // Re-throw with more specific error message
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Cannot connect to AI Commentary service. Please ensure the backend service is running on port 5000.');
    }

    throw error;
  }
};

/**
 * Process raw commentary text into structured sections
 * 
 * @param rawCommentary - Raw commentary text from LLM
 * @returns Processed commentary with structured sections
 */
export const processCommentary = (rawCommentary: string): ProcessedCommentary => {
  try {
    // Split the commentary into sections based on common patterns
    const lines = rawCommentary.split('\n').filter(line => line.trim());
    
    let summary = '';
    const recommendations: string[] = [];
    const risks: string[] = [];
    const keyInsights: string[] = [];
    
    let currentSection = 'summary';
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Skip empty lines and greeting
      if (!trimmedLine || trimmedLine.includes('👋 Hello!')) continue;
      
      // Detect section headers
      if (trimmedLine.toLowerCase().includes('recommendation') || 
          trimmedLine.toLowerCase().includes('suggest')) {
        currentSection = 'recommendations';
        continue;
      } else if (trimmedLine.toLowerCase().includes('risk') || 
                 trimmedLine.toLowerCase().includes('warning') ||
                 trimmedLine.toLowerCase().includes('consider')) {
        currentSection = 'risks';
        continue;
      } else if (trimmedLine.toLowerCase().includes('insight') || 
                 trimmedLine.toLowerCase().includes('key point') ||
                 trimmedLine.toLowerCase().includes('important')) {
        currentSection = 'insights';
        continue;
      }
      
      // Add content to appropriate section
      if (currentSection === 'summary' && !summary) {
        summary = trimmedLine;
      } else if (currentSection === 'recommendations') {
        recommendations.push(trimmedLine.replace(/^[-•*]\s*/, ''));
      } else if (currentSection === 'risks') {
        risks.push(trimmedLine.replace(/^[-•*]\s*/, ''));
      } else if (currentSection === 'insights') {
        keyInsights.push(trimmedLine.replace(/^[-•*]\s*/, ''));
      }
    }
    
    // If no structured content found, use the raw text as summary
    if (!summary && recommendations.length === 0 && risks.length === 0 && keyInsights.length === 0) {
      summary = rawCommentary.replace(/👋 Hello! Here's your policy analysis:\s*/g, '').trim();
    }
    
    // Only return data if we have actual content from the backend
    if (!summary && recommendations.length === 0 && risks.length === 0 && keyInsights.length === 0) {
      throw new Error('No valid commentary data received from backend service.');
    }

    return {
      summary: summary || 'Analysis completed.',
      recommendations: recommendations,
      risks: risks,
      keyInsights: keyInsights
    };
  } catch (error) {
    console.error('❌ Error processing commentary:', error);

    // Re-throw the error instead of returning dummy data
    throw new Error('Failed to process commentary data from backend service.');
  }
};

/**
 * Convert scenario table data to format expected by LLM service
 * 
 * @param tableData - Raw table data from scenario
 * @returns Formatted scenario data for LLM
 */
export const formatScenarioDataForLLM = (tableData: any[]): ScenarioData[] => {
  try {
    return tableData.map((row, index) => ({
      policyYear: row.policyYear || (2025 + index),
      age: row.age || (40 + index),
      plannedPremium: row.plannedPremium || row.premium || '$0',
      netOutlay: row.netOutlay || row.outlay || '$0',
      netSurrenderValue: row.netSurrenderValue || row.surrenderValue || '$0',
      netDeathBenefit: row.netDeathBenefit || row.deathBenefit || '$0'
    }));
  } catch (error) {
    console.error('❌ Error formatting scenario data:', error);

    // Throw error instead of returning sample data
    throw new Error('Failed to format scenario data for AI commentary.');
  }
};
