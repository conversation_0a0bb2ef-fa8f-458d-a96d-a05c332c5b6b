import React from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { DashboardProvider } from './contexts/DashboardContext';
import LoginPage from './components/auth/LoginPage';
import Layout from './components/layout/Layout';

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading, isLoggingOut, logoutCounter } = useAuth();

  // Show loading spinner while checking session or logging out
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h4 className="text-xl font-semibold text-gray-700 mb-2">Loading...</h4>
          <p className="text-gray-600">Please wait while we initialize the application</p>
        </div>
      </div>
    );
  }

  // Show logging out spinner for smooth transition
  if (isLoggingOut) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h4 className="text-xl font-semibold text-gray-700 mb-2">Signing Out...</h4>
          <p className="text-gray-600">Please wait while we sign you out</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    // Use logoutCounter as key to force fresh component mount after logout
    return <LoginPage key={`login-${logoutCounter}`} />;
  }

  return (
    <DashboardProvider key={`dashboard-${logoutCounter}`}>
      <Layout />
    </DashboardProvider>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;