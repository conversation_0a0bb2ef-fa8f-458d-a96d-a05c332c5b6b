
import React, { useState } from 'react';
import { FileText, Eye, Calendar, User, Send } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';
import { formatDateToMMDDYYYY } from '../../utils/dateFormatter';

// Real reports data - only ID-1 report with actual PDF
const reports = [
  {
    id: 1,
    scenarioName: 'Life Insurance Analysis - Base Scenario',
    type: 'Policy Illustration',
    generatedBy: '<PERSON>',
    generatedDate: formatDateToMMDDYYYY('2024-01-15'),
    status: 'Completed',
    fileSize: '2.4 MB',
    pdfUrl: '/Inforce Illustration report V3.0.pdf'
  }
];

const AnalysisReports: React.FC = () => {
  const { selectedCustomerData } = useDashboard();
  const [deliveryMethod, setDeliveryMethod] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'text-green-600 bg-green-100';
      case 'Processing':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // Function to handle PDF viewing - opens in separate tab
  const handleViewPDF = (report: any) => {
    if (report.status === 'Completed') {
      // Open the actual PDF file in a new tab
      window.open(report.pdfUrl, '_blank');
    } else {
      alert('Report is still processing. Please wait for completion.');
    }
  };





  const handleSendDocuments = async () => {
    setIsSending(true);

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      alert('Documents sent successfully!');
    } catch (error) {
      alert('Failed to send documents. Please try again.');
    } finally {
      setIsSending(false);
    }
  };



  return (
    <div className="space-y-6">
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <FileText className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                Generated Reports
              </h2>
            </div>
            <div className="text-sm text-gray-500">
              {reports.length} reports available
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    Scenario Name
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    Generated By
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    Date
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    File Size
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {reports.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="py-12 text-center">
                      <div className="text-gray-500">
                        <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <h3 className="text-lg font-medium mb-2">No Reports Available</h3>
                        <p className="text-sm">Reports will be generated from saved scenarios when the feature is implemented.</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  reports.map((report) => (
                  <tr key={report.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="font-medium text-gray-900">
                        {report.scenarioName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {report.type} Document
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-700">
                          {report.generatedBy}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-700">
                          {report.generatedDate}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(report.status)}`}>
                        {report.status}
                      </span>
                    </td>
                    <td className="py-4 px-4 text-gray-700">
                      {report.fileSize}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewPDF(report)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="View PDF Report"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </Card>

      {/* Document Delivery Section */}
      {/* Allow selecting multiple delivery options */}
      <Card>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Send className="w-6 h-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900">
            Document Delivery
          </h2>
        </div>
          </div>

          {/* Delivery Options */}
          <div className="space-y-4">
        {/* 1. Address by Post */}
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <input
          type="radio"
          name="deliveryMethod_post"
          value="post"
          checked={deliveryMethod.includes('post')}
          onClick={() => {
            setDeliveryMethod(prev =>
              prev.includes('post') ? prev.filter(m => m !== 'post') : [...prev, 'post']
            );
          }}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span className="font-medium text-gray-900">
          Sent through Mail
            </span>
          </div>
          <div className="text-sm text-gray-500">
            <div>{selectedCustomerData?.details?.Address || 'No address available'}</div>
            {(selectedCustomerData?.details?.City || selectedCustomerData?.details?.State) && (
              <div>
                {selectedCustomerData?.details?.City}
                {selectedCustomerData?.details?.City && selectedCustomerData?.details?.State && ', '}
                {selectedCustomerData?.details?.State}
              </div>
            )}
          </div>
        </div>

        {/* 2. Share through Email */}
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <input
          type="radio"
          name="deliveryMethod_email"
          value="email"
          checked={deliveryMethod.includes('email')}
          onClick={() => {
            setDeliveryMethod(prev =>
              prev.includes('email') ? prev.filter(m => m !== 'email') : [...prev, 'email']
            );
          }}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span className="font-medium text-gray-900">
          Share through Email
            </span>
          </div>
          <div className="text-sm text-gray-500">
            {selectedCustomerData?.details?.Email || 'No email available'}
          </div>
        </div>

        {/* 3. Share through WhatsApp */}
        <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <input
          type="radio"
          name="deliveryMethod_whatsapp"
          value="whatsapp"
          checked={deliveryMethod.includes('whatsapp')}
          onClick={() => {
            setDeliveryMethod(prev =>
              prev.includes('whatsapp') ? prev.filter(m => m !== 'whatsapp') : [...prev, 'whatsapp']
            );
          }}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span className="font-medium text-gray-900">
          Share through WhatsApp
            </span>
          </div>
          <div className="text-sm text-gray-500">
            {selectedCustomerData?.details?.Phone || 'No phone number available'}
          </div>
        </div>
          </div>

          {/* Send Button */}
          <div className="mt-6">
        <Button
          onClick={handleSendDocuments}
          disabled={isSending || deliveryMethod.length === 0}
          className="w-full"
        >
          {isSending ? (
            <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          <span>Sending...</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
          <Send className="w-4 h-4" />
          <span>Send Documents</span>
            </div>
          )}
        </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AnalysisReports;
