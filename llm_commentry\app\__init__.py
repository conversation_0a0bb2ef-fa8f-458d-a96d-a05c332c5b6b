from flask import Flask
from flask_cors import CORS
from config.config import Config

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Enable CORS for all routes
    CORS(app, origins=["http://localhost:5173", "http://localhost:3000", "http://127.0.0.1:5173"])

    from app.routes import commentary_bp
    app.register_blueprint(commentary_bp, url_prefix='/api')

    return app
