<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Persistent Storage Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .storage-keys {
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Persistent Storage Test</h1>
        <p>This test verifies that the application correctly stores and retrieves data across page refreshes.</p>
        
        <div class="test-section">
            <div class="test-title">📊 Current Storage Status</div>
            <button onclick="checkStorage()">Check Current Storage</button>
            <button onclick="simulateDataEntry()">Simulate Data Entry</button>
            <button onclick="clearStorage()">Clear All Storage</button>
            <div id="storage-status"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 Persistence Test</div>
            <p>1. Click "Simulate Data Entry" to create test data</p>
            <p>2. Refresh the page (F5 or Ctrl+R)</p>
            <p>3. Check if data persists after refresh</p>
            <div id="persistence-test-result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">🔍 Storage Keys</div>
            <div id="storage-keys" class="storage-keys"></div>
        </div>
    </div>

    <script>
        // Storage keys that should be used by the application
        const EXPECTED_STORAGE_KEYS = {
            DASHBOARD_STATE: 'insuranceApp_dashboardState',
            ACTIVE_TAB: 'insuranceApp_activeTab',
            CURRENT_POLICY: 'insuranceApp_currentPolicy',
            SCENARIOS: 'insuranceApp_scenarios',
            SELECTED_SCENARIOS: 'insuranceApp_selectedScenarios',
            SELECTED_CUSTOMER_DATA: 'insuranceApp_selectedCustomerData',
            SELECTED_POLICY_DATA: 'insuranceApp_selectedPolicyData',
            POLICY_SEARCH_FORM_DATA: 'insuranceApp_policySearchFormData',
            ALLOWED_ILLUSTRATION_TYPES: 'insuranceApp_allowedIllustrationTypes'
        };

        function checkStorage() {
            const statusDiv = document.getElementById('storage-status');
            const keysDiv = document.getElementById('storage-keys');
            
            let html = '<div class="info"><strong>Current localStorage contents:</strong><br>';
            let foundKeys = 0;
            
            // Check each expected key
            Object.entries(EXPECTED_STORAGE_KEYS).forEach(([name, key]) => {
                const value = localStorage.getItem(key);
                if (value) {
                    foundKeys++;
                    html += `✅ ${name}: ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}<br>`;
                } else {
                    html += `❌ ${name}: Not found<br>`;
                }
            });
            
            html += `</div>`;
            statusDiv.innerHTML = html;
            
            // Show all localStorage keys
            const allKeys = Object.keys(localStorage);
            keysDiv.innerHTML = `<strong>All localStorage keys (${allKeys.length}):</strong><br>` + 
                               allKeys.map(key => `• ${key}`).join('<br>');
            
            // Show summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = foundKeys > 0 ? 'success' : 'error';
            summaryDiv.innerHTML = `<strong>Summary:</strong> ${foundKeys}/${Object.keys(EXPECTED_STORAGE_KEYS).length} expected keys found`;
            statusDiv.appendChild(summaryDiv);
        }

        function simulateDataEntry() {
            // Simulate the data that would be stored by the application
            const testData = {
                activeTab: 'face-amount',
                currentPolicy: {
                    id: '1',
                    name: 'Test Policy',
                    description: 'Test policy for persistence'
                },
                scenarios: [
                    {
                        id: 'test-scenario-1',
                        name: 'Test Scenario',
                        type: 'face-amount',
                        created: new Date().toISOString()
                    }
                ],
                selectedCustomerData: {
                    name: 'John Doe',
                    policyNumber: '12345',
                    customerId: '1'
                },
                policySearchFormData: {
                    customerName: 'John Doe',
                    policyNumber: '12345',
                    customerId: '1'
                },
                allowedIllustrationTypes: [1, 2, 3, 4]
            };

            // Store test data
            Object.entries(EXPECTED_STORAGE_KEYS).forEach(([name, key]) => {
                const dataKey = name.toLowerCase().replace('_', '');
                if (testData[dataKey]) {
                    localStorage.setItem(key, JSON.stringify(testData[dataKey]));
                }
            });

            // Show success message
            const statusDiv = document.getElementById('storage-status');
            statusDiv.innerHTML = '<div class="success">✅ Test data has been stored! Now refresh the page to test persistence.</div>';
            
            // Auto-check storage after simulation
            setTimeout(checkStorage, 1000);
        }

        function clearStorage() {
            // Clear only application-specific keys
            Object.values(EXPECTED_STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            
            const statusDiv = document.getElementById('storage-status');
            statusDiv.innerHTML = '<div class="info">🧹 Application storage cleared!</div>';
            
            // Auto-check storage after clearing
            setTimeout(checkStorage, 500);
        }

        // Check if this is a page refresh with existing data
        function checkPersistenceOnLoad() {
            const hasData = Object.values(EXPECTED_STORAGE_KEYS).some(key => 
                localStorage.getItem(key) !== null
            );
            
            if (hasData) {
                const resultDiv = document.getElementById('persistence-test-result');
                resultDiv.innerHTML = '<div class="success">✅ SUCCESS: Data persisted across page refresh!</div>';
            }
        }

        // Run checks on page load
        window.onload = function() {
            checkStorage();
            checkPersistenceOnLoad();
        };
    </script>
</body>
</html>
