/**
 * Browser Compatibility Utilities
 * Provides functions to detect browser capabilities and ensure cross-browser compatibility
 */

/**
 * Detect if the current browser is Safari
 */
export const isSafari = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const userAgent = window.navigator.userAgent;
  return /Safari/.test(userAgent) && !/Chrome/.test(userAgent) && !/Chromium/.test(userAgent);
};

/**
 * Detect Safari version
 */
export const getSafariVersion = (): number | null => {
  if (!isSafari()) return null;
  
  const userAgent = window.navigator.userAgent;
  const match = userAgent.match(/Version\/(\d+)/);
  return match ? parseInt(match[1], 10) : null;
};

/**
 * Check if the browser supports modern JavaScript features
 */
export const checkModernJSSupport = (): {
  optionalChaining: boolean;
  nullishCoalescing: boolean;
  intlNumberFormat: boolean;
  dateConstructor: boolean;
  parseFloat: boolean;
} => {
  const support = {
    optionalChaining: false,
    nullishCoalescing: false,
    intlNumberFormat: false,
    dateConstructor: false,
    parseFloat: false
  };

  try {
    // Test optional chaining
    const testObj: any = {};
    support.optionalChaining = testObj?.nonExistent?.property === undefined;
  } catch (e) {
    support.optionalChaining = false;
  }

  try {
    // Test nullish coalescing
    const testValue = null ?? 'default';
    support.nullishCoalescing = testValue === 'default';
  } catch (e) {
    support.nullishCoalescing = false;
  }

  try {
    // Test Intl.NumberFormat
    const formatter = new Intl.NumberFormat('en-US');
    support.intlNumberFormat = formatter.format(1000) === '1,000';
  } catch (e) {
    support.intlNumberFormat = false;
  }

  try {
    // Test Date constructor with different formats
    const date1 = new Date('2024/01/01');
    const date2 = new Date(2024, 0, 1);
    support.dateConstructor = !isNaN(date1.getTime()) && !isNaN(date2.getTime());
  } catch (e) {
    support.dateConstructor = false;
  }

  try {
    // Test parseFloat
    const parsed = parseFloat('123.45');
    support.parseFloat = parsed === 123.45;
  } catch (e) {
    support.parseFloat = false;
  }

  return support;
};

/**
 * Safe date parsing that works across all browsers
 */
export const safeDateParse = (dateInput: any): Date | null => {
  if (!dateInput) return null;

  let dateStr = String(dateInput).trim();
  if (!dateStr) return null;

  try {
    // Remove timezone suffixes
    dateStr = dateStr.replace(/\s*(UTC|GMT|EST|PST|CST|MST).*$/i, '').trim();
    
    // Try different parsing strategies
    const strategies = [
      // Strategy 1: MM-DD-YYYY or MM/DD/YYYY
      () => {
        const match = dateStr.match(/^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/);
        if (match) {
          const [, month, day, year] = match;
          return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        }
        return null;
      },
      
      // Strategy 2: DD.MM.YYYY
      () => {
        const match = dateStr.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
        if (match) {
          const [, day, month, year] = match;
          return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        }
        return null;
      },
      
      // Strategy 3: YYYY-MM-DD
      () => {
        const match = dateStr.match(/^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/);
        if (match) {
          const [, year, month, day] = match;
          return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        }
        return null;
      },
      
      // Strategy 4: ISO format
      () => {
        const match = dateStr.match(/^(\d{4})-(\d{2})-(\d{2})T/);
        if (match) {
          const [, year, month, day] = match;
          return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        }
        return null;
      }
    ];

    for (const strategy of strategies) {
      const result = strategy();
      if (result && !isNaN(result.getTime())) {
        return result;
      }
    }

    return null;
  } catch (error) {
    console.error('safeDateParse error:', error);
    return null;
  }
};

/**
 * Safe number parsing that works across all browsers
 */
export const safeNumberParse = (value: any): number | null => {
  if (value === null || value === undefined || value === '') {
    return null;
  }

  try {
    // Convert to string and clean
    const str = String(value).replace(/[^0-9.-]/g, '');
    if (!str) return null;

    const parsed = parseFloat(str);
    return isNaN(parsed) ? null : parsed;
  } catch (error) {
    console.error('safeNumberParse error:', error);
    return null;
  }
};

/**
 * Safe currency formatting that works across all browsers
 */
export const safeCurrencyFormat = (value: number | string | null | undefined): string => {
  const numValue = safeNumberParse(value);
  if (numValue === null) return '$0.00';

  try {
    // Use Intl.NumberFormat if available (better browser support)
    if (typeof Intl !== 'undefined' && Intl.NumberFormat) {
      const formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
      return formatter.format(numValue);
    } else {
      // Fallback for older browsers
      const rounded = Math.round(numValue * 100) / 100;
      const parts = rounded.toString().split('.');
      const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      const decimalPart = parts[1] ? parts[1].padEnd(2, '0') : '00';
      return `$${integerPart}.${decimalPart}`;
    }
  } catch (error) {
    console.error('safeCurrencyFormat error:', error);
    return '$0.00';
  }
};

/**
 * Run compatibility tests and log results
 */
export const runCompatibilityTests = (): void => {
  console.log('=== BROWSER COMPATIBILITY TESTS ===');
  console.log('Browser:', navigator.userAgent);
  console.log('Is Safari:', isSafari());
  console.log('Safari Version:', getSafariVersion());
  
  const jsSupport = checkModernJSSupport();
  console.log('JavaScript Support:', jsSupport);
  
  // Test date parsing
  const testDates = ['2024-01-01', '01/01/2024', '01.01.2024', '2024/01/01'];
  console.log('Date parsing tests:');
  testDates.forEach(dateStr => {
    const parsed = safeDateParse(dateStr);
    console.log(`  ${dateStr} -> ${parsed ? parsed.toISOString() : 'null'}`);
  });
  
  // Test number parsing
  const testNumbers = ['1000', '1,000', '$1,000.50', '1000.50', 'invalid'];
  console.log('Number parsing tests:');
  testNumbers.forEach(numStr => {
    const parsed = safeNumberParse(numStr);
    console.log(`  ${numStr} -> ${parsed}`);
  });
  
  // Test currency formatting
  const testCurrencies = [1000, 1000.5, 1000.567, 0, null, undefined];
  console.log('Currency formatting tests:');
  testCurrencies.forEach(value => {
    const formatted = safeCurrencyFormat(value);
    console.log(`  ${value} -> ${formatted}`);
  });
  
  console.log('=== END COMPATIBILITY TESTS ===');
};
